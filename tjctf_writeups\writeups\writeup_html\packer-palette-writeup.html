<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TJCTF 2024 - Packet Palette (Forensics) - TRIADA CTF Writeup</title>
    <link rel="stylesheet" href="competition-styles.css">
</head>
<body>
    <div class="writeup-container">
        <header class="writeup-header">
            <h1 class="writeup-title">TJCTF 2024 - Packet Palette (Forensics)</h1>
            <div class="challenge-meta">
                <div class="meta-item"><span class="meta-label">Category:</span> Forensics</div>
<div class="meta-item"><span class="meta-label">Points:</span> 392</div>
<div class="meta-item"><span class="meta-label">Solves:</span> 76</div>
                <div class="meta-item"><span class="meta-label">Team:</span> TRIADA</div>
            </div>
        </header>

        <main class="writeup-content">
            <div class="toc">
                <h3>Table of Contents</h3>
                <ul>
                    <li><a href="#challenge">Challenge Description</a></li>
                    <li><a href="#analysis">Analysis</a></li>
                    <li><a href="#solution">Solution</a></li>
                    <li><a href="#flag">Flag</a></li>
                </ul>
            </div>

            
<strong>Challenge:</strong> Packet Palette  
<strong>Category:</strong> Forensics  
<strong>Points:</strong> 392  
<strong>Solves:</strong> 76  

<h2>Challenge Description</h2>

<p>Someone tried to reinvent USB-over-IP… poorly. Can you sift through their knockoff protocol?</p>

<strong>Files:</strong> <code>chall.pcapng</code>

<h2>Solution Overview</h2>

<p>This challenge involves analyzing a network packet capture file containing a custom protocol that transmits a PNG image. We need to reverse engineer the protocol and extract the image to find the flag.</p>

<h2>Step 1: Initial Analysis</h2>

<p>First, let's examine what we have:</p>

<div class="code-block"><pre><code class="language-bash">file chall.pcapng</code></pre></div>

This confirms it's a packet capture file. Let's use <code>tshark</code> to get a quick overview:

<div class="code-block"><pre><code class="language-bash">tshark -r chall.pcapng -c 10</code></pre></div>

<strong>Output:</strong>
<div class="code-block"><pre><code class="language-text">1   0.000000   ********** → **********   TCP 72 31337 → 31337 [PSH, ACK] Seq=1 Ack=1 Win=8192 Len=18
2   0.000373   ********** → **********   TCP 566 [TCP Previous segment not captured] 31337 → 31337 [PSH, ACK] Seq=1001 Ack=2 Win=8192 Len=512
<p>...</code></pre></div></p>

<strong>Key Observations:</strong>
<ul>
<li>Communication between <code>**********</code> and <code>**********</code></li>
<li>Using TCP port <code>31337</code> (a common "elite" port in CTFs)</li>
<li>Multiple packets with 512-byte payloads</li>
</ul>

<p>Let's check the total number of packets:</p>

<div class="code-block"><pre><code class="language-bash">tshark -r chall.pcapng | wc -l
<h1>Output: 23</code></pre></div></h1>

<p>Only 23 packets total, so this is manageable to analyze.</p>

<h2>Step 2: Protocol Analysis</h2>

<p>Let's examine the packet contents in hex format:</p>

<div class="code-block"><pre><code class="language-bash">tshark -r chall.pcapng -x</code></pre></div>

Looking at the first few packets, we can see:

<strong>Packet 1:</strong>
<div class="code-block"><pre><code class="language-text">0030  20 00 c4 39 00 00 55 53 42 49 50 3a 44 45 56 49    ..9..USBIP:DEVI
<p>0040  43 45 5f 52 45 41 44 59                           CE_READY</code></pre></div></p>

<strong>Packet 2:</strong>
<div class="code-block"><pre><code class="language-text">0030  20 00 0c 07 00 00 55 53 42 49 00 00 00 15 00 00    .....USBI......
<p>0040  01 f4 89 50 4e 47 0d 0a 1a 0a 00 00 00 0d 49 48   ...PNG........IH</code></pre></div></p>

<strong>Last Packet:</strong>
<div class="code-block"><pre><code class="language-text">0030  20 00 71 d2 00 00 55 53 42 49 50 3a 44 4f 4e 45    .q...USBIP:DONE</code></pre></div>

<strong>Protocol Structure Discovered:</strong>
1. <strong>Handshake:</strong> <code>USBIP:DEVICE_READY</code>
2. <strong>Data packets:</strong> <code>USBI</code> + metadata + payload
3. <strong>End marker:</strong> <code>USBIP:DONE</code>

<strong>Important Discovery:</strong> In packet 2, we see <code>89 50 4E 47</code> which is the PNG file signature! This means a PNG image is being transmitted.

<h2>Step 3: Understanding the USBI Protocol</h2>

Looking at the USBI packets more carefully:

<div class="code-block"><pre><code class="language-text">USBI + [2 bytes sequence] + [2 bytes unknown] + [4 bytes length] + [payload data]</code></pre></div>

For example, in packet 2:
- <code>USBI</code> - Protocol identifier
- <code>00 00</code> - Sequence number (0)
- <code>00 15</code> - Unknown field
- <code>00 00 01 f4</code> - Length (500 bytes)
- <code>89 50 4E 47...</code> - PNG data starts here

<h2>Step 4: Writing the Extraction Script</h2>

Now we need to extract and reassemble the PNG data. Create a file called <code>extract_png.py</code>:

<div class="code-block"><pre><code class="language-python">#!/usr/bin/env python3

import struct
from scapy.all import *

def extract_usbi_data(pcap_file):
    &quot;&quot;&quot;Extract data from USBI packets and reconstruct the PNG file&quot;&quot;&quot;
    packets = rdpcap(pcap_file)
    usbi_data = {}

    for packet in packets:
        if packet.haslayer(Raw):
            payload = bytes(packet[Raw])

            # Look for USBI header
            if payload.startswith(b&#x27;USBI&#x27;):
                # Parse the USBI header
                if len(payload) &gt;= 12:
                    # Extract sequence number (first 2 bytes after USBI)
                    seq_num = struct.unpack(&#x27;&gt;H&#x27;, payload[4:6])[0]
                    # Extract length (4 bytes starting at offset 8)
                    length = struct.unpack(&#x27;&gt;I&#x27;, payload[8:12])[0]
                    # Extract the actual data
                    data = payload[12:12+length]

                    print(f&quot;Packet {seq_num}: {len(data)} bytes&quot;)
                    usbi_data[seq_num] = data

    # Reconstruct by concatenating in sequence order
    reconstructed_data = b&#x27;&#x27;
    for seq_num in sorted(usbi_data.keys()):
        reconstructed_data += usbi_data[seq_num]

    return reconstructed_data

def main():
    print(&quot;Extracting PNG from USBI packets...&quot;)
    png_data = extract_usbi_data(&#x27;chall.pcapng&#x27;)

    print(f&quot;Total data: {len(png_data)} bytes&quot;)

    # Verify PNG format
    if png_data.startswith(b&#x27;\x89PNG&#x27;):
        print(&quot;✓ Valid PNG header found!&quot;)

        with open(&#x27;flag.png&#x27;, &#x27;wb&#x27;) as f:
            f.write(png_data)
        print(&quot;✓ Saved as flag.png&quot;)
    else:
        print(&quot;❌ Invalid PNG data&quot;)

if __name__ == &#x27;__main__&#x27;:
<p>    main()</code></pre></div></p>

<strong>Key Points About the Script:</strong>
<ul>
<li>Uses Scapy to parse the packet capture</li>
<li>Identifies USBI packets by looking for the "USBI" header</li>
<li>Extracts sequence numbers and data lengths from the protocol</li>
<li>Reassembles data in the correct order</li>
<li>Validates PNG format by checking magic bytes</li>
</ul>

<h2>Step 5: Running the Script</h2>

<p>First, install the required Python library:</p>

<div class="code-block"><pre><code class="language-bash">pip install scapy</code></pre></div>

Then run the extraction script:

<div class="code-block"><pre><code class="language-bash">python3 extract_png.py</code></pre></div>

<strong>Expected Output:</strong>
<div class="code-block"><pre><code class="language-text">Extracting PNG from USBI packets...
Packet 0: 500 bytes
Packet 1: 500 bytes
Packet 2: 500 bytes
...
Packet 20: 375 bytes
Total data: 10375 bytes
✓ Valid PNG header found!
<p>✓ Saved as flag.png</code></pre></div></p>

<h2>Step 6: Getting the Flag</h2>

<p>Open the extracted PNG file to see the flag:</p>

<div class="code-block"><pre><code class="language-bash"># Linux
xdg-open flag.png

<h1>macOS</h1>
open flag.png

<h1>Windows</h1>
<p>start flag.png</code></pre></div></p>

<p>The image will display the flag in the format: <code>tjctf{...}</code></p>

<h2>What We Learned</h2>

<p>This challenge taught us several important concepts:</p>

<h3>1. Custom Protocol Analysis</h3>
<ul>
<li>How to identify protocol patterns in network traffic</li>
<li>Understanding packet structure and sequencing</li>
<li>Recognizing when data is being transmitted across multiple packets</li>
</ul>

<h3>2. File Format Recognition</h3>
<ul>
<li>PNG files start with specific magic bytes: <code>\x89PNG</code></li>
<li>File signatures help identify data types in network streams</li>
<li>Proper file reconstruction requires maintaining data integrity</li>
</ul>

<h3>3. Binary Data Handling</h3>
<ul>
<li>Using Python's <code>struct</code> module to parse binary protocols</li>
<li>Understanding big-endian vs little-endian byte ordering</li>
<li>Converting raw bytes to meaningful data structures</li>
</ul>

<h3>4. Forensics Methodology</h3>
<ul>
<li>Start with high-level analysis (packet counts, protocols)</li>
<li>Drill down into specific packet contents</li>
<li>Look for patterns and known file signatures</li>
<li>Validate reconstructed data</li>
</ul>

<h2>Tools and Dependencies</h2>

<h3>Required Tools:</h3>
<ul>
<li><code>tshark</code> or <code>Wireshark</code> - For packet analysis</li>
<li><code>Python 3</code> - For scripting</li>
<li><code>scapy</code> library - For packet parsing</li>
</ul>

<h3>Installation:</h3>
<div class="code-block"><pre><code class="language-bash"># Install tshark (part of Wireshark)
sudo apt-get install tshark  # Ubuntu/Debian
brew install wireshark       # macOS

<h1>Install Python dependencies</h1>
<p>pip install scapy</code></pre></div></p>

<h2>Alternative Solutions</h2>

<h3>Method 1: Manual Wireshark Analysis</h3>
<ol>
<li>Open <code>chall.pcapng</code> in Wireshark</li>
<li>Filter for packets containing "USBI"</li>
<li>Manually extract hex data from each packet</li>
<li>Concatenate the data and save as PNG</li>
</ol>

<h3>Method 2: Command Line with tshark</h3>
<div class="code-block"><pre><code class="language-bash"># Extract raw packet data
tshark -r chall.pcapng -T fields -e data.data &gt; raw_data.txt

<h1>Process with custom script to extract USBI payloads</code></pre></div></h1>

<h3>Method 3: Python with Raw Socket Parsing</h3>
<p>Instead of Scapy, use Python's built-in socket libraries for more control over packet parsing.</p>

<h2>Common Pitfalls</h2>

<ol>
<li><strong>Incorrect Byte Order:</strong> Make sure to use big-endian (<code>></code>) format for network protocols</li>
<li><strong>Missing Packets:</strong> Verify all sequence numbers are present</li>
<li><strong>Wrong Offset:</strong> Protocol headers can vary - always verify field positions</li>
<li><strong>File Corruption:</strong> Check both PNG header and footer for complete reconstruction</li>
</ol>

<h2>Flag Format</h2>
<p>The flag follows the standard TJCTF format: <code>tjctf{...}</code></p>

<h2>Conclusion</h2>

<p>This challenge demonstrates a realistic scenario where attackers might use custom protocols to exfiltrate data. The key skills developed include:</p>

<ul>
<li>Reverse engineering unknown network protocols</li>
<li>Reconstructing files from network traffic</li>
<li>Using Python for forensics automation</li>
<li>Understanding common file formats and signatures</li>
</ul>

<p>These skills are essential for network forensics, malware analysis, and incident response in cybersecurity.</p>


            <div class="flag-section" id="flag">
                <h2>Flag</h2>
                <div class="flag">🚩 tjctf{...}</div>
            </div>
        </main>

        <footer class="writeup-footer">
            <p><strong>Team TRIADA</strong> - Competitive CTF Team</p>
            <p>Writeup prepared for competition submission</p>
        </footer>
    </div>
</body>
</html>