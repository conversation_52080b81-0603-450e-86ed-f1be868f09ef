<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TJCTF 2024 - The Art of War (Crypto) - TRIADA CTF Writeup</title>
    <link rel="stylesheet" href="writeup-styles.css">
</head>
<body>
    <nav class="writeup-nav">
        <a href="../../../index.html" class="back-btn">← Back to Home</a>
    </nav>
    
    <div class="writeup-container">
        <header class="writeup-header">
            <h1 class="writeup-title">TJCTF 2024 - The Art of War (Crypto)</h1>
            <div class="challenge-meta">
                <div class="meta-item"><span class="meta-label">Category:</span> Crypto</div>
<div class="meta-item"><span class="meta-label">Points:</span> 259</div>
<div class="meta-item"><span class="meta-label">Solves:</span> 135</div>
<div class="meta-item"><span class="meta-label">Author:</span> thegreenmallard</div>
            </div>
        </header>
        
        <main class="writeup-content">
            
<strong>Category:</strong> Crypto  
<strong>Points:</strong> 259  
<strong>Solves:</strong> 135  
<strong>Author:</strong> thegreenmallard  

<h2>Challenge Description</h2>

<blockquote>"In the midst of chaos, there is also opportunity"</blockquote>
<p>> </p>
<blockquote>Sun Tzu, The Art of War</blockquote>

<strong>Files provided:</strong>
<ul>
<li><code>main.py</code> - The encryption script</li>
<li><code>output.txt</code> - The encrypted output</li>
</ul>

<h2>Initial Analysis</h2>

<p>Let's examine the provided files to understand the challenge:</p>

<h3>main.py</h3>
<pre><code class="language-python">from Crypto.Util.number import bytes_to_long, getPrime, long_to_bytes
import time

flag = open(&#x27;flag.txt&#x27;, &#x27;rb&#x27;).read()
m = bytes_to_long(flag)

e = getPrime(8)
print(f&#x27;e = {e}&#x27;)

def generate_key():
    p, q = getPrime(256), getPrime(256)
    while (p - 1) % e == 0:
        p = getPrime(256)
    while (q - 1) % e == 0:
        q = getPrime(256)
    return p * q
    
for i in range(e):
    n = generate_key()
    c = pow(m, e, n)
    print(f&#x27;n{i} = {n}&#x27;)
<p>    print(f&#x27;c{i} = {c}&#x27;)</code></pre></p>

<h3>Key Observations</h3>

<ol>
<li><strong>Small exponent</strong>: <code>e</code> is an 8-bit prime (from <code>getPrime(8)</code>)</li>
<li><strong>Multiple encryptions</strong>: The same message <code>m</code> is encrypted <code>e</code> times with different RSA moduli</li>
<li><strong>Same exponent</strong>: All encryptions use the same exponent <code>e</code></li>
<li><strong>Different moduli</strong>: Each encryption uses a freshly generated RSA modulus <code>n_i = p_i * q_i</code></li>
</ol>

<p>From <code>output.txt</code>, we can see:</p>
<ul>
<li><code>e = 229</code> (a prime number)</li>
<li>229 different RSA moduli (<code>n0</code> through <code>n228</code>)</li>
<li>229 corresponding ciphertexts (<code>c0</code> through <code>c228</code>)</li>
</ul>

<h2>Vulnerability: Chinese Remainder Theorem Attack</h2>

<p>This setup is vulnerable to a <strong>Chinese Remainder Theorem (CRT) attack</strong> on RSA. Here's why:</p>

<h3>The Mathematical Foundation</h3>

<p>We have the system of congruences:</p>
<pre><code class="language-text">m^e ≡ c_0 (mod n_0)
m^e ≡ c_1 (mod n_1)
...
<p>m^e ≡ c_228 (mod n_228)</code></pre></p>

<p>Since:</p>
<ol>
<li>We have exactly <code>e = 229</code> congruences</li>
<li>All moduli are pairwise coprime (different RSA moduli)</li>
<li>The message <code>m</code> is likely much smaller than the product of all moduli</li>
</ol>

<p>We can use the Chinese Remainder Theorem to find <code>m^e mod (n_0 <em> n_1 </em> ... * n_228)</code>.</p>

<h3>Why This Works</h3>

<p>If <code>m^e < n_0 <em> n_1 </em> ... * n_228</code>, then:</p>
<pre><code class="language-text">m^e ≡ CRT_result (mod ∏n_i)</code></pre>

becomes:
<pre><code class="language-text">m^e = CRT_result</code></pre>

We can then take the <code>e</code>-th root to recover <code>m</code>.

<h2>Solution Implementation</h2>

Here's the complete solution:

<pre><code class="language-python">#!/usr/bin/env python3

from Crypto.Util.number import long_to_bytes
import re

def extended_gcd(a, b):
    if a == 0:
        return b, 0, 1
    gcd, x1, y1 = extended_gcd(b % a, a)
    x = y1 - (b // a) * x1
    y = x1
    return gcd, x, y

def chinese_remainder_theorem(remainders, moduli):
    total = 0
    prod = 1
    for m in moduli:
        prod *= m

    for r, m in zip(remainders, moduli):
        p = prod // m
        _, inv, _ = extended_gcd(p, m)
        total += r <em> inv </em> p

    return total % prod

def nth_root(x, n):
    if x &lt;= 1:
        return x

    # Newton&#x27;s method for nth root
    bit_length = x.bit_length()
    guess = 1 &lt;&lt; ((bit_length + n - 1) // n)

    prev_guess = 0
    while guess != prev_guess:
        prev_guess = guess
        guess = ((n - 1) * guess + x // (guess ** (n - 1))) // n

    # Verify result
    while guess ** n &gt; x:
        guess -= 1

    return guess

<h1>Parse output.txt</h1>
with open(&#x27;output.txt&#x27;, &#x27;r&#x27;) as f:
    content = f.read()

<h1>Extract e, n values, and c values</h1>
e = int(re.search(r&#x27;e = (\d+)&#x27;, content).group(1))
n_values = [int(re.search(rf&#x27;n{i} = (\d+)&#x27;, content).group(1)) for i in range(e)]
c_values = [int(re.search(rf&#x27;c{i} = (\d+)&#x27;, content).group(1)) for i in range(e)]

<h1>Apply CRT to solve m^e ≡ c_i (mod n_i)</h1>
m_to_e = chinese_remainder_theorem(c_values, n_values)

<h1>Take eth root to get m</h1>
m = nth_root(m_to_e, e)

<h1>Convert to flag</h1>
flag = long_to_bytes(m)
<p>print(flag.decode())</code></pre></p>

<h2>Execution and Result</h2>

<p>Running the solution:</p>

<pre><code class="language-bash">$ python3 decode.py
<p>tjctf{the_greatest_victory_is_that_which_require_no_battle}</code></pre></p>

<h2>Flag</h2>

<pre><code class="language-text">tjctf{the_greatest_victory_is_that_which_require_no_battle}</code></pre>

<h2>Key Insights</h2>

1. <strong>Theme Connection</strong>: The flag is a quote from Sun Tzu's "The Art of War": "The greatest victory is that which requires no battle" - fitting the challenge theme perfectly.

2. <strong>Attack Efficiency</strong>: This attack demonstrates how using the same small exponent across multiple RSA instances can be catastrophic, even when each individual RSA key is secure.

3. <strong>Mathematical Beauty</strong>: The Chinese Remainder Theorem provides an elegant solution to what initially appears to be an intractable problem.

<h2>Lessons Learned</h2>

- <strong>Never reuse small exponents</strong> across multiple RSA encryptions of the same message
- <strong>Small exponents</strong> in RSA can be dangerous when combined with poor implementation practices
- <strong>Mathematical attacks</strong> often provide the most elegant solutions to cryptographic challenges

This challenge beautifully demonstrates how mathematical theory (CRT) can be applied to break seemingly secure cryptographic implementations through careful analysis of the underlying mathematical structure.

            <div class="flag">🚩 tjctf{the_greatest_victory_is_that_which_require_no_battle}</div>
        </main>
    </div>
</body>
</html>