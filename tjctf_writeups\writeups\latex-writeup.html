<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>TeXploit CTF Challenge Writeup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 2rem;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            max-width: 900px;
            margin: auto;
            box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
        }
        pre {
            background-color: #272822;
            color: #f8f8f2;
            padding: 1rem;
            border-radius: 4px;
            overflow-x: auto;
        }
        h1, h2 {
            color: #007acc;
        }
        code {
            background-color: #eee;
            padding: 2px 4px;
            border-radius: 4px;
            font-family: monospace;
        }
        .flag {
            font-size: 1.2rem;
            font-weight: bold;
            color: green;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CTF Writeup: TeXploit (Web Challenge)</h1>
        <p><strong>Category:</strong> Web</p>
        <p><strong>Points:</strong> 228</p>
        <p><strong>Solves:</strong> 152</p>

        <h2>Challenge Description</h2>
        <p>
            The challenge presents a minimal LaTeX compiler web interface where users can input raw LaTeX code,
            and receive a compiled PDF. It states that if there's an error during compilation, the log file will be shown.
            The flag is located at <code>/flag.txt</code>.
        </p>

        <h2>Objective</h2>
        <p>Read the contents of <code>/flag.txt</code> and leak it through the LaTeX error logs.</p>

        <h2>Initial Thoughts</h2>
        <p>
            LaTeX is a Turing-complete language with file I/O capabilities. The initial approach using
            <code>\input{/flag.txt}</code> failed because the keyword <code>input</code> was blacklisted.
        </p>

        <h2>Exploit Strategy</h2>
        <p>
            We moved to lower-level TeX primitives that are often overlooked by filters:
        </p>
        <ul>
            <li><code>\openin</code>: Opens a file for reading</li>
            <li><code>\read</code>: Reads a line into a macro</li>
            <li><code>\errmessage</code>: Forces an error message (visible in log)</li>
        </ul>

        <h2>Final Payload</h2>
        <pre>\newread\file
\openin\file=/flag.txt
\read\file to \line
\errmessage{FLAG: \line}</pre>

        <h2>Explanation</h2>
        <ol>
            <li>Creates a new file handle with <code>\newread</code>.</li>
            <li>Opens <code>/flag.txt</code> for reading.</li>
            <li>Reads one line of the file into the macro <code>\line</code>.</li>
            <li>Triggers an error message that includes the flag content.</li>
        </ol>

        <h2>Result</h2>
        <p>
            The server compiles the LaTeX, hits our forced error, and prints the following in the log output:
        </p>
        <pre>! FLAG: tjctf{f1l3_i0_1n_l4t3x?} .
l.8 \errmessage{FLAG: \line}</pre>

        <p class="flag">✅ Flag: <code>tjctf{f1l3_i0_1n_l4t3x?}</code></p>

        <h2>Takeaways</h2>
        <ul>
            <li>Even with blacklists, LaTeX has deep internals that allow file access.</li>
            <li>Error-based data exfiltration is a powerful and stealthy approach.</li>
            <li>Sanitization needs to go beyond superficial command keywords.</li>
        </ul>

        <h2>Credits</h2>
        <p>Exploited and written by <strong>Adarsh SR</strong> for the CTF challenge "TeXploit".</p>
    </div>
</body>
</html>
