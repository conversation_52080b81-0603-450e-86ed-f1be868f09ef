@import url('https://fonts.cdnfonts.com/css/technos');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600&display=swap');

:root {
    --primary-red: #ff0033;
    --dark-red: #cc0026;
    --black: #0a0a0a;
    --white: #fff;
    --gray: #333;
    --light-gray: #555;
    --very-light-gray: #999;
    --transparent-red: rgba(255, 0, 51, 0.1);
    --primary-font: 'Montserrat', sans-serif;
    --logo-font: 'Technos', sans-serif;
    --code-font: 'JetBrains Mono', monospace;
    --transition-speed: 0.3s;
    --glow-effect: 0 0 15px rgba(255, 0, 51, 0.5);
    --text-glow: 0 0 10px rgba(255, 0, 51, 0.3);
    --neon-border: 1px solid rgba(255, 0, 51, 0.3);
    --code-bg: rgba(20, 20, 20, 0.8);
    --code-border: 1px solid rgba(255, 255, 255, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--primary-font);
    background-color: var(--black);
    color: var(--white);
    min-height: 100vh;
    line-height: 1.6;
    font-size: 16px;
    padding: 20px;
}

.writeup-container {
    max-width: 1000px;
    margin: 0 auto;
    background: rgba(10, 10, 10, 0.8);
    border: var(--neon-border);
    padding: 40px;
    border-radius: 8px;
}

/* Header Styles */
.writeup-header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: var(--neon-border);
}

.writeup-title {
    font-family: var(--logo-font);
    font-size: 36px;
    color: var(--white);
    margin-bottom: 20px;
    text-shadow: var(--text-glow);
    letter-spacing: 1px;
}

.challenge-meta {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.meta-item {
    background: var(--transparent-red);
    border: var(--neon-border);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.meta-label {
    color: var(--primary-red);
    font-weight: 600;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--white);
    margin: 30px 0 15px 0;
    font-weight: 600;
}

h1 {
    font-size: 32px;
    color: var(--primary-red);
    border-bottom: 2px solid var(--primary-red);
    padding-bottom: 10px;
}

h2 {
    font-size: 26px;
    color: var(--primary-red);
    margin-top: 40px;
}

h3 {
    font-size: 22px;
    color: var(--white);
}

h4 {
    font-size: 18px;
    color: var(--very-light-gray);
}

p {
    margin-bottom: 16px;
    line-height: 1.7;
}

/* Lists */
ul, ol {
    margin: 16px 0;
    padding-left: 30px;
}

li {
    margin-bottom: 8px;
    line-height: 1.6;
}

/* Links */
a {
    color: var(--primary-red);
    text-decoration: none;
    transition: all var(--transition-speed);
}

a:hover {
    color: var(--white);
    text-shadow: var(--text-glow);
}

/* Code Blocks */
code {
    font-family: var(--code-font);
    background: var(--code-bg);
    border: var(--code-border);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 14px;
    color: var(--white);
}

pre {
    background: var(--code-bg);
    border: var(--neon-border);
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    overflow-x: auto;
    position: relative;
}

pre code {
    background: none;
    border: none;
    padding: 0;
    font-size: 14px;
    line-height: 1.5;
    color: var(--white);
}

/* Blockquotes */
blockquote {
    border-left: 4px solid var(--primary-red);
    background: rgba(255, 0, 51, 0.05);
    padding: 16px 20px;
    margin: 20px 0;
    font-style: italic;
    border-radius: 0 8px 8px 0;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background: var(--code-bg);
    border: var(--neon-border);
    border-radius: 8px;
    overflow: hidden;
}

th, td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

th {
    background: rgba(255, 0, 51, 0.1);
    color: var(--primary-red);
    font-weight: 600;
}

/* Special Elements */
.flag {
    background: linear-gradient(45deg, var(--primary-red), var(--dark-red));
    color: var(--white);
    padding: 16px 24px;
    border-radius: 8px;
    font-family: var(--code-font);
    font-weight: 600;
    text-align: center;
    margin: 20px 0;
    border: var(--neon-border);
    box-shadow: var(--glow-effect);
}

.solution-section {
    background: rgba(20, 20, 20, 0.6);
    border: var(--neon-border);
    border-radius: 8px;
    padding: 24px;
    margin: 30px 0;
}

.solution-section h3 {
    color: var(--primary-red);
    margin-top: 0;
}

/* Navigation */
.writeup-nav {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
}

.back-btn {
    background: var(--transparent-red);
    border: var(--neon-border);
    color: var(--white);
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-speed);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.back-btn:hover {
    background: var(--primary-red);
    transform: translateY(-2px);
    box-shadow: var(--glow-effect);
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .writeup-container {
        padding: 20px;
    }
    
    .writeup-title {
        font-size: 28px;
    }
    
    .challenge-meta {
        flex-direction: column;
        gap: 10px;
    }
    
    h1 {
        font-size: 24px;
    }
    
    h2 {
        font-size: 20px;
    }
    
    pre {
        padding: 15px;
        font-size: 12px;
    }
    
    .writeup-nav {
        position: static;
        margin-bottom: 20px;
    }
}

/* Syntax highlighting for code blocks */
.language-python .keyword { color: #ff6b6b; }
.language-python .string { color: #4ecdc4; }
.language-python .comment { color: var(--very-light-gray); }
.language-python .number { color: #ffe66d; }

.language-bash .command { color: #4ecdc4; }
.language-bash .flag { color: #ffe66d; }

/* Scroll styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--black);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-red);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-red);
}
