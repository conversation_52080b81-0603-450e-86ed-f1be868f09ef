# TJCTF 2024 - Close Secrets Writeup

**Challenge**: Close Secrets  
**Category**: Crypto  
**Points**: 326  
**Solves**: 107  

## Challenge Description

> I tried to make my Diffie-Hellman implementation a bit more interesting, but maybe I went too far?
> 
> Can you make sense of this custom cryptography and decode the encrypted flag?

**Files provided:**
- `params.txt` - Contains the Di<PERSON><PERSON>-<PERSON><PERSON> parameters
- `encrypt.py` - The encryption implementation
- `enc_flag` - The encrypted flag

## Understanding the Challenge

### What is <PERSON><PERSON><PERSON>-<PERSON><PERSON>?

Diffie-Hellman is a method for two parties to agree on a shared secret key over an insecure channel. Here's how it normally works:

1. **Public parameters**: Everyone knows `p` (a large prime) and `g` (a generator)
2. **Private keys**: <PERSON> picks secret `a`, <PERSON> picks secret `b`
3. **Public keys**: <PERSON> computes `A = g^a mod p`, <PERSON> computes `B = g^b mod p`
4. **Shared secret**: <PERSON> computes `B^a mod p`, <PERSON> computes `A^b mod p`
5. **Magic**: Both get the same result: `g^(ab) mod p`

The security relies on the fact that even if you know `g`, `p`, `A`, and `B`, it's very hard to figure out `a` or `b` (this is called the discrete logarithm problem).

### Analyzing the Code

Let's look at the key generation function in `encrypt.py`:

```python
def generate_dh_key():
    p = number.getPrime(1024)  # Generate a 1024-bit prime
    g = number.getPrime(1024)  # Generate another 1024-bit prime
    a = randint(p - 10, p)     # 🚨 VULNERABILITY: Only 10 possible values!
    b = randint(g - 10, g)     # 🚨 VULNERABILITY: Only 10 possible values!
    u = pow(g, a, p)           # u = g^a mod p
    v = pow(g, b, p)           # v = g^b mod p
    key = pow(v, a, p)         # shared key = v^a mod p
    b_key = pow(u, b, p)       # verification = u^b mod p
    if key != b_key:
        sys.exit(1)
    return p, g, u, v, key
```

**The Critical Flaw**: Instead of choosing `a` and `b` from the full range `[1, p-1]` (which would have ~2^1024 possibilities), the code only chooses from tiny ranges:
- `a` can only be one of: `p-10, p-9, p-8, p-7, p-6, p-5, p-4, p-3, p-2, p-1` (10 values)
- `b` can only be one of: `g-10, g-9, g-8, g-7, g-6, g-5, g-4, g-3, g-2, g-1` (10 values)

This means there are only **10 × 10 = 100** possible combinations to try!

### Understanding the Encryption Process

The flag is encrypted using a two-step process:

1. **XOR Encryption** (`dynamic_xor_encrypt`):
   - Creates a key by hashing the shared secret: `SHA256(shared_key)`
   - XORs each byte of the **reversed** flag with the key bytes
   
2. **Outer Encryption** (`encrypt_outer`):
   - For each byte value, applies: `(value + key%256) * key`

## The Attack Strategy

Since there are only 100 possible key combinations, we can try them all:

1. **Brute force the private keys**: Try all combinations of `a ∈ [p-10, p-1]` and `b ∈ [g-10, g-1]`
2. **Check if valid**: For each combination, compute `u = g^a mod p` and `v = g^b mod p`
3. **Match with given values**: If our computed `u` and `v` match the ones in `params.txt`, we found the right keys!
4. **Compute shared secret**: Calculate `shared_key = v^a mod p`
5. **Decrypt the flag**: Reverse the encryption process

## Solution Implementation

Here's the complete solution:

```python
#!/usr/bin/env python3
import hashlib

def decrypt_outer(cipher_ords, key):
    """Reverse the outer encryption: (val + key_offset) * key"""
    plaintext = []
    key_offset = key % 256
    for val in cipher_ords:
        # Reverse: original = (val / key) - key_offset
        original = (val // key) - key_offset
        plaintext.append(original)
    return plaintext

def dynamic_xor_decrypt(encrypted_ords, text_key_bytes):
    """Reverse the XOR encryption and un-reverse the bytes"""
    decrypted_bytes = []
    key_length = len(text_key_bytes)
    for i, encrypted_ord in enumerate(encrypted_ords):
        key_byte = text_key_bytes[i % key_length]
        decrypted_bytes.append(encrypted_ord ^ key_byte)
    # Un-reverse the bytes (they were reversed during encryption)
    return bytes(decrypted_bytes[::-1])

def solve_challenge():
    # Read the Diffie-Hellman parameters
    with open("params.txt", "r") as f:
        lines = f.readlines()
    
    p = int(lines[0].split(" = ")[1].strip())
    g = int(lines[1].split(" = ")[1].strip())
    u = int(lines[2].split(" = ")[1].strip())
    v = int(lines[3].split(" = ")[1].strip())
    
    # Read the encrypted flag
    with open("enc_flag", "r") as f:
        enc_flag_str = f.read().strip()
    final_cipher = eval(enc_flag_str)  # Parse the list
    
    print("Brute forcing the private keys...")
    
    # Try all possible values of a and b
    for a in range(p - 10, p):      # 10 possible values for a
        for b in range(g - 10, g):  # 10 possible values for b
            try:
                # Calculate what u and v should be with these a, b values
                expected_u = pow(g, a, p)
                expected_v = pow(g, b, p)
                
                # Check if this matches the given u, v
                if expected_u == u and expected_v == v:
                    print(f"Found the private keys!")
                    
                    # Calculate the shared secret
                    shared_key = pow(v, a, p)
                    
                    # Generate the XOR key (same as in encryption)
                    xor_key_str = hashlib.sha256(str(shared_key).encode()).hexdigest()
                    xor_key_bytes = xor_key_str.encode('utf-8')
                    
                    # Decrypt step by step
                    intermediate_ords = decrypt_outer(final_cipher, shared_key)
                    flag_bytes = dynamic_xor_decrypt(intermediate_ords, xor_key_bytes)
                    flag = flag_bytes.decode('utf-8')
                    
                    print(f"Decrypted flag: {flag}")
                    return flag
                    
            except Exception:
                continue  # Try next combination
    
    print("No valid solution found")
    return None

if __name__ == "__main__":
    solve_challenge()
```

## Running the Solution

```bash
$ python3 solve.py
Brute forcing the private keys...
Found the private keys!
Decrypted flag: tjctf{sm4ll_r4ng3_sh0rt_s3cr3t}
```

## Key Takeaways

1. **Cryptographic implementations must use proper key sizes**: The private keys should be chosen from the full range `[1, p-1]`, not a tiny subset.

2. **Small key spaces are vulnerable to brute force**: 100 combinations is trivial for a computer to try.

3. **The flag name is a hint**: `sm4ll_r4ng3_sh0rt_s3cr3t` directly references the vulnerability - small range leading to short (weak) secrets.

4. **Defense**: Always use cryptographically secure random number generation with appropriate ranges for your key material.

This challenge demonstrates why proper implementation of cryptographic algorithms is crucial - even a small mistake can completely break the security!

## Step-by-Step Walkthrough for Beginners

### Step 1: Examine the Files

First, let's look at what we have:

**params.txt** contains the public Diffie-Hellman parameters:
```
p = 170414318260705733587875759581770955570309017044222937085191604024452735032594248474424765923836540211737893584439554899006773968654769091193247412851040473946419617178998073469868100539248246324253541060008089222137232131934348865470429102329705344623611275490470993627132873486693712828328643742288205112527
g = 120681139203436291164378184116247954042577742288954642499192807256993763587666398677530059224138614478338902785098981693557339976850974900098504529593380063851724924557945231653341683157340782295208957489088101760434538987711049950242033559644930710185044163047334693996198198372552023208414727714764489712283
u = 135668771029510263907110237169555858182389140035220525526216692019541532644619777145586283806534328053310689073913522508004464538889367992552446578372649985255898549272854575377510712592134690407014502401681964878932616905735136520060908211096236619268316849967775899568661131681489146190493710890951160724706
v = 165036785985552337550703112630578321968495250714033243796585786662838937148680751454783550779786426910035205483848329274328823513229605340921993107676999077541506833174273521350152646080628330616219078232643906110969196674275384683101159565935895506360072481873027262155137472248477311708158177572618168100749
```

**enc_flag** contains a long list of numbers - this is our encrypted flag.

### Step 2: Understand the Vulnerability

The key insight is in this part of the code:
```python
a = randint(p - 10, p)  # Should be randint(1, p-1)
b = randint(g - 10, g)  # Should be randint(1, g-1)
```

In proper Diffie-Hellman, `a` and `b` should be random numbers between 1 and p-1. But here, they're only chosen from the last 10 numbers before p and g respectively.

### Step 3: The Brute Force Attack

Since there are only 10 × 10 = 100 combinations, we can try them all:

```python
for a in range(p - 10, p):      # Try a = p-10, p-9, ..., p-1
    for b in range(g - 10, g):  # Try b = g-10, g-9, ..., g-1
        # Check if this combination gives us the right u and v
        if pow(g, a, p) == u and pow(g, b, p) == v:
            # Found it! Now we can calculate the shared key
            shared_key = pow(v, a, p)
```

### Step 4: Reverse the Encryption

Once we have the shared key, we need to undo the encryption:

1. **Undo outer encryption**: Each number was transformed by `(original + offset) * key`
2. **Undo XOR encryption**: XOR with the key again and reverse the byte order

### Step 5: Get the Flag

Run the solution and get: `tjctf{sm4ll_r4ng3_sh0rt_s3cr3t}`

## Why This Attack Works

- **Normal Diffie-Hellman**: ~2^1024 possible private keys (impossible to brute force)
- **This implementation**: Only 100 possible combinations (trivial to brute force)
- **Time complexity**: O(100) instead of O(2^1024)

The lesson: In cryptography, implementation details matter enormously!
