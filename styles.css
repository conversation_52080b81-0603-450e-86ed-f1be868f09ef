@import url('https://fonts.cdnfonts.com/css/technos');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

:root {
    --primary-red: #ff0033;
    --dark-red: #cc0026;
    --black: #0a0a0a;
    --white: #fff;
    --gray: #333;
    --light-gray: #555;
    --very-light-gray: #999;
    --transparent-red: rgba(255, 0, 51, 0.1);
    --primary-font: 'Montserrat', sans-serif;
    --logo-font: 'Technos', sans-serif;
    --transition-speed: 0.3s;
    --glow-effect: 0 0 15px rgba(255, 0, 51, 0.5);
    --text-glow: 0 0 10px rgba(255, 0, 51, 0.3);
    --neon-border: 1px solid rgba(255, 0, 51, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--primary-font);
    background-color: var(--black);
    color: var(--white);
    min-height: 100vh;
    overflow-x: hidden;
    line-height: 1.6;
    font-size: 16px;
}

input,
select,
textarea,
button {
    font-size: 16px;
}

header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: var(--neon-border);
}

.brand {
    font-family: var(--logo-font);
    font-size: 24px;
    font-weight: bold;
    color: var(--white);
    position: relative;
    z-index: 1001;
    letter-spacing: 1px;
    text-shadow: var(--text-glow);
    padding: 10px 20px;
    border: var(--neon-border);
    clip-path: polygon(0 0, 100% 0, 95% 100%, 5% 100%);
    text-decoration: none;
    display: block;
    transition: all var(--transition-speed);
}

.brand:hover {
    color: var(--white);
    text-shadow: 0 0 15px rgba(255, 0, 51, 0.7);
    border-color: rgba(255, 0, 51, 0.5);
}

nav {
    position: relative;
}

.nav-toggle {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.nav-toggle-label {
    display: none;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 30px;
    padding: 10px 20px;
    background: rgba(10, 10, 10, 0.8);
    border: var(--neon-border);
}

nav a {
    color: var(--white);
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    transition: all var(--transition-speed);
    padding: 5px 0;
    position: relative;
}

nav a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--primary-red);
    transition: width var(--transition-speed);
}

nav a:hover::after,
nav a.active::after {
    width: 100%;
}

nav a:hover,
nav a.active {
    color: var(--primary-red);
}

.container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    padding-top: 80px;
    position: relative;
    z-index: 1;
}

.home-container {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 50px;
    min-height: calc(100vh - 80px);
    display: flex;
}

.logo-section {
    width: 45%;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-background {
    width: 80%;
    height: 80%;
    background: url('images/stroke.png') no-repeat center center;
    background-size: contain;
}

.content-section {
    width: 45%;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: rgba(10, 10, 10, 0.8);
    border: var(--neon-border);
    margin-left: 50px;
}

.main-logo {
    font-size: 64px;
    margin-bottom: 30px;
    letter-spacing: 2px;
}

.description {
    font-size: 18px;
    line-height: 1.8;
    margin-bottom: 40px;
    font-weight: 300;
    color: var(--white);
    padding: 20px 25px;
    background: rgba(20, 20, 20, 0.5);
    border: var(--neon-border);
}

.cta-buttons {
    display: flex;
    gap: 20px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    border: none;
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-speed);
    letter-spacing: 1px;
}

.btn-primary {
    background-color: var(--primary-red);
    color: var(--white);
    border: var(--neon-border);
}

.btn-secondary {
    background-color: transparent;
    color: var(--white);
    border: var(--neon-border);
}

.btn-primary:hover {
    background-color: var(--dark-red);
    transform: translateY(-2px);
}

.btn-secondary:hover {
    background-color: var(--transparent-red);
    transform: translateY(-2px);
}

.page-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 100px 30px 80px;
}

.page-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 0 20px;
}

.page-header h1 {
    font-size: 48px;
    margin-bottom: 15px;
    letter-spacing: 2px;
}

.page-header h1::after {
    content: '';
    position: absolute;
    width: 60px;
    height: 3px;
    background-color: var(--primary-red);
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.subtitle {
    font-size: 18px;
    color: var(--very-light-gray);
    font-weight: 300;
    margin: 15px 0;
}

.team-status-legend {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 25px;
    background-color: rgba(20, 20, 20, 0.8);
    padding: 10px 25px;
    border-radius: 50px;
    border: var(--neon-border);
    max-width: 250px;
    margin-left: auto;
    margin-right: auto;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #ffffff;
}

.status-dot.active {
    background-color: #4CAF50;
}

.status-dot.inactive {
    background-color: #9e9e9e;
}

.status-text {
    font-size: 14px;
    color: var(--white);
    font-weight: 500;
}

.member-status {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 100;
    background-color: rgba(0, 0, 0, 0.8);
    padding: 4px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    padding: 30px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.team-member {
    background-color: rgba(20, 20, 20, 0.8);
    border: var(--neon-border);
    transition: all var(--transition-speed);
    overflow: hidden;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.team-member:hover {
    transform: translateY(-10px);
}

.member-image {
    height: 280px;
    overflow: hidden;
    position: relative;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.team-member:hover .member-image img {
    transform: scale(1.05);
}

.member-info {
    padding: 25px;
    background: rgba(10, 10, 10, 0.8);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.member-title {
    color: var(--primary-red);
    font-weight: 500;
    margin-bottom: 15px;
    font-size: 16px;
}

.member-bio {
    color: var(--very-light-gray);
    font-weight: 300;
    margin-bottom: 15px;
}

.member-social {
    display: flex;
    gap: 15px;
    margin-top: auto;
    padding-top: 15px;
}

.social-links-home {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.social-icon-home,
.social-icon-footer {
    color: var(--white);
    text-decoration: none;
    font-size: 20px;
    transition: all var(--transition-speed);
    margin: 0 15px 0 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.social-icon-home:hover,
.social-icon-footer:hover {
    color: var(--primary-red);
}

.ctftime-logo {
    height: 20px;
    width: auto;
}

.portfolio-icon {
    color: var(--white);
    transition: all var(--transition-speed);
}

.portfolio-icon:hover {
    color: var(--primary-red);
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 50px;
}

.achievement-card {
    background-color: rgba(20, 20, 20, 0.8);
    border: var(--neon-border);
    padding: 30px;
    transition: transform 0.3s ease;
}

.achievement-card:hover {
    transform: translateY(-5px);
}

.achievement-card h3 {
    font-size: 22px;
    margin-bottom: 15px;
    color: var(--primary-red);
}

.achievement-card p {
    font-size: 16px;
    line-height: 1.6;
    color: var(--white);
    margin-bottom: 0;
}

.blogs-container {
    min-height: 100vh;
    padding-top: 80px;
    background: var(--black);
}

.blogs-hero {
    padding: 40px 20px 30px;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: rgba(255, 0, 51, 0.1);
    border: 1px solid rgba(255, 0, 51, 0.3);
    border-radius: 50px;
    padding: 6px 16px;
    margin-bottom: 20px;
    font-size: 12px;
    font-weight: 500;
    color: var(--primary-red);
}

.badge-dot {
    width: 6px;
    height: 6px;
    background: var(--primary-red);
    border-radius: 50%;
}

.hero-title {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 12px;
    color: var(--white);
    line-height: 1.2;
    letter-spacing: 1px;
}

.hero-subtitle {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
    font-weight: 300;
}

.blogs-main {
    padding: 20px 20px 40px;
}

.content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.status-card {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 24px;
    transition: all 0.3s ease;
}

.status-card:hover {
    border-color: rgba(255, 0, 51, 0.3);
}

.status-header {
    display: flex;
    align-items: flex-start;
    gap: 16px;
}

.status-icon {
    width: 48px;
    height: 48px;
    background: var(--primary-red);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
}

.status-text h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--white);
    margin-bottom: 8px;
    line-height: 1.3;
}

.status-text p {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.5;
    margin: 0;
}

.preview-section {
    text-align: center;
}

.section-title {
    font-size: 22px;
    font-weight: 600;
    color: var(--white);
    margin-bottom: 24px;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: var(--primary-red);
}

.preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
}

.preview-card {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 20px 16px;
    text-align: center;
    transition: all 0.3s ease;
}

.preview-card:hover {
    transform: translateY(-4px);
    border-color: rgba(255, 0, 51, 0.3);
}

.card-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 0, 51, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    font-size: 18px;
    color: var(--primary-red);
}

.preview-card h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--white);
    margin-bottom: 8px;
}

.preview-card p {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.5;
    margin: 0;
}

.notification-section {
    display: flex;
    justify-content: center;
}

.notification-card {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    max-width: 400px;
    width: 100%;
}

.notification-card h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--white);
    margin-bottom: 8px;
}

.notification-card p {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 20px;
    line-height: 1.5;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 12px;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-link:hover {
    background: rgba(255, 0, 51, 0.1);
    border-color: rgba(255, 0, 51, 0.3);
    color: var(--primary-red);
}

footer {
    background-color: rgba(10, 10, 10, 0.95);
    padding: 40px 0;
    border-top: var(--neon-border);
    width: 100%;
    position: relative;
    z-index: 10;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-logo {
    font-family: var(--logo-font);
    font-size: 24px;
    font-weight: bold;
    letter-spacing: 1px;
    text-shadow: var(--text-glow);
    padding: 10px 20px;
    border: var(--neon-border);
    clip-path: polygon(0 0, 100% 0, 95% 100%, 5% 100%);
}

.footer-links {
    display: flex;
    gap: 20px;
    padding: 10px 20px;
    background: rgba(20, 20, 20, 0.5);
    border: var(--neon-border);
}

.footer-links a {
    color: var(--white);
    text-decoration: none;
    transition: all var(--transition-speed);
    font-weight: 500;
}

.footer-links a:hover {
    color: var(--primary-red);
}

.footer-copyright {
    color: var(--very-light-gray);
    font-size: 14px;
}

@media screen and (max-width: 768px) {
    .blogs-hero {
        padding: 30px 20px;
    }

    .hero-title {
        font-size: 28px;
    }

    .hero-subtitle {
        font-size: 14px;
    }

    .preview-grid {
        grid-template-columns: 1fr;
    }
}

@media screen and (max-width: 1200px) {
    .team-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media screen and (max-width: 1024px) {
    .home-container {
        flex-direction: column;
        padding: 20px;
    }

    .logo-section {
        width: 100%;
        height: 400px;
        margin-bottom: 30px;
    }

    .content-section {
        width: 100%;
        margin-left: 0;
        margin-bottom: 30px;
    }

    .main-logo {
        font-size: 48px;
    }

    .achievements-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        padding: 30px;
    }
}

@media screen and (max-width: 768px) {
    header {
        padding: 12px 15px;
    }

    .brand {
        font-size: 20px;
        padding: 6px 12px;
        border: none;
        text-shadow: 0 0 10px rgba(255, 0, 51, 0.5);
    }

    .nav-toggle-label {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        width: 35px;
        height: 35px;
        background-color: rgba(20, 20, 20, 0.5);
        border-radius: 4px;
        border: 1px solid rgba(255, 0, 51, 0.2);
    }

    .nav-toggle-label span,
    .nav-toggle-label span::before,
    .nav-toggle-label span::after {
        display: block;
        position: absolute;
        height: 2px;
        width: 22px;
        background-color: var(--primary-red);
        transition: all 0.3s ease;
    }

    .nav-toggle-label span {
        top: 50%;
        transform: translateY(-50%);
    }

    .nav-toggle-label span::before,
    .nav-toggle-label span::after {
        content: '';
    }

    .nav-toggle-label span::before {
        top: -7px;
    }

    .nav-toggle-label span::after {
        top: 7px;
    }

    .nav-toggle:checked~ul {
        right: 0;
    }

    nav ul {
        position: fixed;
        top: 0;
        right: -100%;
        flex-direction: column;
        width: 100%;
        height: 100vh;
        background: rgba(10, 10, 10, 0.98);
        z-index: 1001;
        justify-content: center;
        align-items: center;
        padding: 80px 30px 30px;
        transition: all 0.3s ease;
    }

    .home-container {
        padding: 15px;
    }

    .logo-section {
        height: 200px;
        margin-top: 80px;
    }

    .content-section {
        padding: 20px;
        background-color: transparent;
        border: none;
    }

    .main-logo {
        font-size: 42px;
        text-align: center;
        margin-bottom: 20px;
    }

    .btn {
        width: calc(50% - 5px);
        text-align: center;
        padding: 12px;
    }

    .team-grid {
        grid-template-columns: 1fr;
        padding: 15px;
    }

    footer {
        padding: 30px 0;
    }

    .footer-content {
        padding: 0 20px;
        text-align: center;
        justify-content: center;
    }

    .footer-logo {
        padding: 8px 15px;
    }

    .footer-links {
        width: 100%;
        justify-content: center;
        padding: 10px;
    }
}

@media screen and (max-width: 576px) {
    .main-logo {
        font-size: 36px;
    }

    .logo-section {
        height: 160px;
        margin-top: 60px;
    }

    nav a {
        font-size: 18px;
        padding: 15px 0;
        text-align: center;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    nav a::after {
        display: none;
    }

    .btn {
        width: 100%;
        padding: 14px;
    }

    .achievements-grid {
        grid-template-columns: 1fr;
        padding: 20px 15px;
    }

    .page-header h1 {
        font-size: 28px;
    }

    .page-container {
        padding: 100px 15px 40px;
    }

    .footer-links {
        flex-direction: column;
        gap: 15px;
    }
}

.member-info h3 {
    color: var(--white);
    font-size: 20px;
    margin-bottom: 5px;
    font-weight: 600;
}

.social-icon {
    color: var(--white);
    text-decoration: none;
    font-size: 17px;
    transition: all var(--transition-speed);
    margin-right: 15px;
}

.social-icon:hover {
    color: var(--primary-red);
}

.social-links-footer {
    display: flex;
    gap: 15px;
}

img {
    max-width: 100%;
    height: auto;
}