# Neural Network Inversion Challenge - TJCTF 2024

## Challenge Overview

**Challenge Name:** neural-inversion  
**Category:** Reverse Engineering  
**Points:** 430  
**Solves:** 77  

**Description:**  
Use the provided model.npz to recover the hidden ASCII flag by algebraically inverting the network's layers.

**Files Provided:**
- `model.npz` - Neural network model containing weights, biases, and target output

## Understanding the Challenge

This challenge involves **neural network inversion** - the process of working backwards through a trained neural network to recover the original input that produced a given output. The flag is encoded as the input to a 2-layer neural network, and we need to mathematically reverse the network's computations.

## Step 1: Analyzing the Neural Network

First, let's examine what's inside the model file:

```python
import numpy as np

# Load the model
model = np.load("model.npz")
print("Keys in model:", list(model.keys()))

# Examine each component
for key in model.keys():
    data = model[key]
    print(f"{key}: shape {data.shape}")
```

**Output:**
```
Keys in model: ['W1', 'b1', 'W2', 'b2', 'y']
W1: shape (30, 30)  # First layer weights
b1: shape (30,)    # First layer biases  
W2: shape (30, 30)  # Second layer weights
b2: shape (30,)    # Second layer biases
y: shape (30,)     # Target output
```

## Step 2: Understanding the Network Architecture

The neural network has this structure:
```
Input (x) → [W1, b1] → Activation → [W2, b2] → Activation → Output (y)
    30         30                      30                      30
```

The mathematical operations are:
1. `z1 = W1 @ x + b1` (linear transformation)
2. `h1 = activation(z1)` (activation function)
3. `z2 = W2 @ h1 + b2` (linear transformation)  
4. `y = activation(z2)` (activation function)

## Step 3: Determining the Activation Function

Looking at the target output `y`, all values are between 0 and 1:
```python
print(f"y range: [{np.min(y):.6f}, {np.max(y):.6f}]")
# Output: y range: [0.001663, 0.998565]
```

This strongly suggests **sigmoid activation** (not ReLU), since:
- Sigmoid outputs are always in range (0, 1)
- ReLU outputs can be any positive value

The sigmoid function is: `σ(x) = 1 / (1 + e^(-x))`

## Step 4: Mathematical Inversion Process

To recover the input `x`, we need to work backwards through each layer:

### Step 4.1: Invert the Final Sigmoid
Given: `y = σ(z2)`  
We need: `z2 = σ^(-1)(y)`

The inverse sigmoid function is:
```python
def sigmoid_inverse(y):
    y = np.clip(y, 1e-15, 1-1e-15)  # Avoid numerical issues
    return np.log(y / (1 - y))

z2 = sigmoid_inverse(y)
```

### Step 4.2: Solve for Hidden Layer
Given: `z2 = W2 @ h1 + b2`  
We need: `h1 = W2^(-1) @ (z2 - b2)`

```python
h1 = np.linalg.inv(W2) @ (z2 - b2)
```

### Step 4.3: Invert the Hidden Sigmoid  
Given: `h1 = σ(z1)`  
We need: `z1 = σ^(-1)(h1)`

```python
h1_clipped = np.clip(h1, 1e-15, 1-1e-15)  # Ensure valid range
z1 = sigmoid_inverse(h1_clipped)
```

### Step 4.4: Solve for Input
Given: `z1 = W1 @ x + b1`  
We need: `x = W1^(-1) @ (z1 - b1)`

```python
x = np.linalg.inv(W1) @ (z1 - b1)
```

## Step 5: Complete Solution Code

```python
#!/usr/bin/env python3
import numpy as np

def sigmoid_inverse(y):
    """Inverse of sigmoid function"""
    y = np.clip(y, 1e-15, 1-1e-15)
    return np.log(y / (1 - y))

def invert_neural_network():
    """Invert 2-layer sigmoid neural network to recover input"""
    # Load model
    model = np.load("model.npz")
    W1, b1, W2, b2, y = model["W1"], model["b1"], model["W2"], model["b2"], model["y"]
    
    # Step 1: Invert final sigmoid layer
    z2 = sigmoid_inverse(y)
    
    # Step 2: Solve for hidden layer: h1 = W2^(-1) @ (z2 - b2)
    h1 = np.linalg.inv(W2) @ (z2 - b2)
    
    # Step 3: Invert hidden sigmoid layer
    h1_clipped = np.clip(h1, 1e-15, 1-1e-15)
    z1 = sigmoid_inverse(h1_clipped)
    
    # Step 4: Solve for input: x = W1^(-1) @ (z1 - b1)
    x = np.linalg.inv(W1) @ (z1 - b1)
    
    return x

def extract_flag(x):
    """Extract ASCII flag from recovered input"""
    # Scale by 127 and convert to ASCII
    ascii_vals = np.round(x * 127).astype(int)
    flag = ''.join([chr(c) for c in ascii_vals])
    return flag

if __name__ == "__main__":
    # Invert the neural network
    x = invert_neural_network()
    
    # Extract the flag
    flag = extract_flag(x)
    print(f"Flag: {flag}")
```

## Step 6: Flag Extraction

The recovered input `x` contains values in the range [0.377953, 0.984252]. To convert these to ASCII characters:

1. **Scale by 127:** `ascii_vals = round(x * 127)`
2. **Convert to characters:** `chr(ascii_val)` for each value

The scaling factor of 127 was discovered through experimentation with different scales (1, 10, 100, 127, 255) until readable text appeared.

## Final Result

**Flag:** `tjctf{m0d3l_1nv3rs10n_f9a93qw}`

## Key Learning Points

1. **Neural Network Inversion:** This technique can recover inputs from outputs when you have access to the model weights
2. **Activation Function Identification:** Output ranges give clues about activation functions
3. **Mathematical Precision:** Numerical stability is crucial when inverting functions
4. **Matrix Inversion:** Linear algebra operations are fundamental to neural network inversion
5. **Data Encoding:** Flags can be encoded in various ways - here as normalized ASCII values

## Why This Works

Neural network inversion is possible here because:
- The network is relatively simple (2 layers)
- All weight matrices are invertible (square and full rank)
- We have exact target outputs (no noise)
- The activation function (sigmoid) has a well-defined inverse

In practice, this technique is used in:
- Adversarial attacks on neural networks
- Privacy analysis of machine learning models
- Understanding what neural networks have learned

## Alternative Approaches

If the direct inversion hadn't worked, other approaches could include:
- **Gradient-based optimization:** Use gradient descent to find input that produces target output
- **Brute force search:** Try different input combinations (computationally expensive)
- **Genetic algorithms:** Evolutionary approach to find the input

This challenge demonstrates the importance of understanding both the mathematical foundations of neural networks and potential security implications when model weights are exposed.
