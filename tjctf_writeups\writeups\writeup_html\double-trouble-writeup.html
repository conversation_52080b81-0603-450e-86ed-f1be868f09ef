<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Double Trouble - Crypto CTF Writeup - TRIADA CTF Writeup</title>
    <link rel="stylesheet" href="writeup-styles.css">
</head>
<body>
    <nav class="writeup-nav">
        <a href="../../../index.html" class="back-btn">← Back to Home</a>
    </nav>
    
    <div class="writeup-container">
        <header class="writeup-header">
            <h1 class="writeup-title">Double Trouble - Crypto CTF Writeup</h1>
            <div class="challenge-meta">
                <div class="meta-item"><span class="meta-label">Category:</span> Crypto</div>
<div class="meta-item"><span class="meta-label">Points:</span> 427</div>
<div class="meta-item"><span class="meta-label">Solves:</span> 80</div>
            </div>
        </header>
        
        <main class="writeup-content">
            
<strong>Challenge:</strong> double-trouble  
<strong>Category:</strong> Crypto  
<strong>Points:</strong> 427  
<strong>Solves:</strong> 80  
<strong>Description:</strong> Twice the encryption, half the security.

<h2>Challenge Files</h2>

<ul>
<li><code>enc.py</code> - The encryption script</li>
<li><code>out.txt</code> - Contains encrypted data (example plaintext and flag)</li>
</ul>

<h2>Initial Analysis</h2>

<p>Let's start by examining the encryption script to understand what we're dealing with:</p>

<pre><code class="language-python">from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import random

def gen():
    myrandom = random.Random(42)
    k1 = myrandom.randbytes(8)
    choices = list(myrandom.randbytes(6))
    k2 = b&#x27;&#x27;
    for _ in range(8):
        k2 += bytes([choices[random.randint(0, 3)]])
    return k1, k2

def enc(data, k1, k2, k3, k4):
    key1 = k1+k2
    cipher = AES.new(key1, mode=AES.MODE_ECB)
    ct1 = cipher.encrypt(pad(data, 16))
    key2 = k4+k3
    cipher = AES.new(key2, mode=AES.MODE_ECB)
    ct2 = cipher.encrypt(ct1)
    return ct2

k1, k2 = gen()
k3, k4 = gen()

pt = b&quot;example&quot;

with open(&#x27;flag.txt&#x27;) as f:
    flag = f.read().encode()

with open(&#x27;out.txt&#x27;, &quot;w&quot;) as f:
    f.write(enc(pt, k1, k2, k3, k4).hex())
    f.write(&quot;\n&quot;)
<p>    f.write(enc(flag, k1, k2, k3, k4).hex())</code></pre></p>

<p>And the output file contains:</p>
<pre><code class="language-text">7125383e330c692c75e0ee0886ec7779
<p>9ecba853742db726fb39e748a0c5cfd06b682c8f15be13bc8ba2b2304897eca2</code></pre></p>

<h2>Understanding the Encryption</h2>

<h3>Double AES Encryption</h3>
<p>The challenge uses <strong>double encryption</strong> with AES in ECB mode:</p>
<ol>
<li>First encryption: <code>AES_encrypt(plaintext, k1+k2)</code></li>
<li>Second encryption: <code>AES_encrypt(ciphertext1, k4+k3)</code></li>
</ol>

<h3>Key Generation Analysis</h3>
<p>The <code>gen()</code> function is where the vulnerability lies. Let's break it down:</p>

<ol>
<li><strong>Deterministic Part</strong>: <code>myrandom = random.Random(42)</code> creates a seeded random generator</li>
<ul>
</ol>
<li><code>k1 = myrandom.randbytes(8)</code> - Always the same (deterministic)</li>
<li><code>choices = list(myrandom.randbytes(6))</code> - Always the same 6 bytes</li>
</ul>

<ol>
<li><strong>Vulnerable Part</strong>: The loop that builds <code>k2</code></li>
   <pre><code class="language-python">   for _ in range(8):
       k2 += bytes([choices[random.randint(0, 3)]])
   ``<code>
   - Uses </code>random.randint(0, 3)<code> which calls the <strong>global</strong> random state
   - Not the seeded </code>myrandom<code> instance!

<h3>The Vulnerability</h3>

The key insight is that:
- </code>k1<code> and </code>k3<code> are identical (both use </code>Random(42)<code>)
- </code>choices<code> array is the same for both calls
- </code>k2<code> and </code>k4<code> are built by selecting from only <strong>4 possible values</strong> each byte
- Each key component has only </code>4^8 = 65,536<code> possible values instead of </code>2^64<code>

<h2>Why Meet-in-the-Middle Works</h2>

Instead of brute forcing </code>4^16 ≈ 4.3 billion<code> combinations, we can use a meet-in-the-middle attack:

<h3>Traditional Brute Force: O(4^16)</h3>
- Try all combinations of k2 and k4
- </code>4^8 × 4^8 = 65,536 × 65,536 = 4,294,967,296<code> combinations

<h3>Meet-in-the-Middle: O(2 × 4^8)</h3>
- Phase 1: Encrypt known plaintext with all possible first keys
- Phase 2: Decrypt ciphertext with all possible second keys
- Look for matching intermediate values
- Total: </code>65,536 + 65,536 = 131,072<code> operations

<h2>Solution Implementation</h2>
</ol>
</code></pre>python
<p>from Crypto.Cipher import AES</p>
<p>from Crypto.Util.Padding import pad, unpad</p>
<p>import random</p>
<p>import itertools</p>

<h1>Read the encrypted data</h1>
<p>with open('out.txt', 'r') as f:</p>
<p>    lines = f.read().strip().split('\n')</p>
<p>    enc_example = bytes.fromhex(lines[0])</p>
<p>    enc_flag = bytes.fromhex(lines[1])</p>

<p>known_pt = b"example"</p>

<h1>Get the deterministic parts</h1>
<p>myrandom = random.Random(42)</p>
<p>k1 = myrandom.randbytes(8)</p>
<p>choices = list(myrandom.randbytes(6))</p>

<p>print(f"k1 = k3: {k1.hex()}")</p>
<p>print(f"choices: {choices}")</p>

<h1>Meet-in-the-middle attack</h1>
<p>intermediate_values = {}</p>

<h1>Phase 1: Encrypt known plaintext with all possible first keys</h1>
<p>print("Phase 1: Computing all possible encryptions with first key...")</p>
<p>for k2_indices in itertools.product(range(4), repeat=8):</p>
<p>    k2 = b''.join(bytes([choices[i]]) for i in k2_indices)</p>
<p>    key1 = k1 + k2</p>
    
<p>    cipher = AES.new(key1, mode=AES.MODE_ECB)</p>
<p>    intermediate = cipher.encrypt(pad(known_pt, 16))</p>
    
<p>    intermediate_values[intermediate] = k2_indices</p>

<h1>Phase 2: Decrypt ciphertext with all possible second keys</h1>
<p>print("Phase 2: Trying all possible decryptions with second key...")</p>
<p>for k4_indices in itertools.product(range(4), repeat=8):</p>
<p>    k4 = b''.join(bytes([choices[i]]) for i in k4_indices)</p>
<p>    key2 = k4 + k1  # k3 = k1</p>
    
<p>    cipher = AES.new(key2, mode=AES.MODE_ECB)</p>
<p>    intermediate = cipher.decrypt(enc_example)</p>
    
<p>    if intermediate in intermediate_values:</p>
<p>        k2_indices = intermediate_values[intermediate]</p>
<p>        k2 = b''.join(bytes([choices[i]]) for i in k2_indices)</p>
        
<p>        print("MATCH FOUND!")</p>
<p>        # Decrypt the flag</p>
<p>        def dec(ct, k1, k2, k3, k4):</p>
<p>            key2 = k4+k3</p>
<p>            cipher = AES.new(key2, mode=AES.MODE_ECB)</p>
<p>            ct1 = cipher.decrypt(ct)</p>
<p>            key1 = k1+k2</p>
<p>            cipher = AES.new(key1, mode=AES.MODE_ECB)</p>
<p>            pt = cipher.decrypt(ct1)</p>
<p>            return unpad(pt, 16)</p>
        
<p>        flag = dec(enc_flag, k1, k2, k1, k4)</p>
<p>        print(f"Flag: {flag.decode()}")</p>
<p>        break</p>
<pre><code class="language-text">
<h2>Key Concepts for Beginners</h2>

<h3>1. Random vs Pseudorandom</h3>
- </code>random.Random(42)<code> creates a <strong>seeded</strong> generator - always produces the same sequence
- </code>random.randint()<code> uses the <strong>global</strong> random state - can vary between runs

<h3>2. Meet-in-the-Middle Attack</h3>
This is a classic cryptographic attack that trades memory for time:
- Instead of trying all </code>N²<code> combinations
- Try all </code>N<code> possibilities for first half, store results
- Try all </code>N<code> possibilities for second half, look for matches
- Reduces complexity from </code>O(N²)<code> to </code>O(N)<code>

<h3>3. Why Double Encryption Failed Here</h3>
- Double encryption can be secure if keys are independent and large
- Here, the key space was artificially reduced to 4^8 per component
- The deterministic key generation made the attack feasible

<h2>Results</h2>

<p>Running the meet-in-the-middle attack:</code></pre></p>
<p>k1 = k3: 9d79b1a37f31801c</p>
<p>k2: 1a1a1a1a67671a06</p>
<p>k4: 1a671a1a06d11a1a</p>
<p>Flag: tjctf{m33t_in_th3_middl3}</p>
<pre><code class="language-text">
<h2>Step-by-Step Solution Process</h2>

<h3>Step 1: Identify the Vulnerability</h3>
1. Notice that </code>gen()<code> uses both seeded and global random
2. Realize that </code>k1 = k3<code> and </code>choices<code> are identical
3. Calculate that each k2/k4 has only 4^8 = 65,536 possibilities

<h3>Step 2: Choose Attack Strategy</h3>
- Brute force: 4^16 ≈ 4.3 billion operations (too slow)
- Meet-in-the-middle: 2 × 4^8 ≈ 131k operations (feasible)

<h3>Step 3: Implement the Attack</h3>
1. Generate all possible intermediate values from first encryption
2. Try all possible second keys and look for matches
3. When match found, reconstruct both keys and decrypt flag

<h3>Step 4: Verify and Extract Flag</h3>
- Verify the solution works on known plaintext
- Apply same keys to decrypt the flag

<h2>Mathematical Analysis</h2>

<h3>Key Space Reduction</h3>
- Normal AES-128: 2^128 ≈ 3.4 × 10^38 possible keys
- This challenge: 4^8 × 4^8 = 2^16 × 2^16 = 2^32 ≈ 4.3 × 10^9 possible key pairs
- Reduction factor: 2^128 / 2^32 = 2^96 ≈ 7.9 × 10^28

<h3>Meet-in-the-Middle Complexity</h3>
- Time complexity: O(2 × 4^8) = O(131,072)
- Space complexity: O(4^8) = O(65,536) for storing intermediate values
- Much better than O(4^16) brute force

<h2>Common Pitfalls and How to Avoid Them</h2>

<h3>1. Misunderstanding Random vs Seeded Random</h3>
<strong>Wrong assumption</strong>: All random calls use the same state
<strong>Correct understanding</strong>: </code>random.Random(42)<code> is independent of global </code>random<code>

<h3>2. Incorrect Key Reconstruction</h3>
<strong>Wrong</strong>: Assuming k1 ≠ k3
<strong>Correct</strong>: Both gen() calls use Random(42), so k1 = k3

<h3>3. Brute Force Instead of Smart Attack</h3>
<strong>Wrong</strong>: Try all 4^16 combinations
<strong>Correct</strong>: Use meet-in-the-middle to reduce to 2 × 4^8

<h2>Tools and Libraries Used</h2>
</code></pre>python
<p>from Crypto.Cipher import AES          # AES encryption/decryption</p>
<p>from Crypto.Util.Padding import pad, unpad  # PKCS7 padding</p>
<p>import random                          # Random number generation</p>
<p>import itertools                       # Efficient iteration over combinations</p>
</code>`<code>

<h2>Alternative Approaches</h2>

<h3>1. Known Plaintext Attack</h3>
<p>Since we have "example" → ciphertext pair, we could:</p>
<ul>
<li>Try to recover keys directly</li>
<li>Use differential cryptanalysis (overkill for this challenge)</li>
</ul>

<h3>2. Statistical Analysis</h3>
<ul>
<li>Analyze the distribution of random.randint(0,3) outputs</li>
<li>Look for patterns in key generation (not needed here)</li>
</ul>

<h3>3. Side-Channel Analysis</h3>
<ul>
<li>In real scenarios, timing attacks might be possible</li>
<li>Not applicable to this static challenge</li>
</ul>

<h2>Lessons Learned</h2>

<ol>
<li><strong>Key Generation is Critical</strong>: Poor key generation can completely break otherwise strong encryption</li>
<li><strong>Reduced Key Spaces</strong>: Limiting choices dramatically reduces security</li>
<li><strong>Meet-in-the-Middle</strong>: A powerful technique when key spaces are manageable</li>
<li><strong>Double Encryption</strong>: Not always better - can introduce new vulnerabilities</li>
<li><strong>Known Plaintext</strong>: Having plaintext-ciphertext pairs greatly aids cryptanalysis</li>
</ol>

<h2>Further Reading</h2>

<ul>
<li><a href="https://en.wikipedia.org/wiki/Meet-in-the-middle_attack">Meet-in-the-Middle Attack on Wikipedia</a></li>
<li><a href="https://en.wikipedia.org/wiki/Block_cipher_mode_of_operation">AES Encryption Modes</a></li>
<li><a href="https://tools.ietf.org/html/rfc4086">Cryptographic Key Generation Best Practices</a></li>
</ul>

<p>The flag </code>tjctf{m33t_in_th3_middl3}` perfectly describes the solution method!</p>

            <div class="flag">🚩 tjctf{m33t_in_th3_middl3}</div>
        </main>
    </div>
</body>
</html>