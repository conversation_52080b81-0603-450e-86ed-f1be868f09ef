<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TJCTF 2024 - Alchemist Recipe Writeup - TRIADA CTF Writeup</title>
    <link rel="stylesheet" href="competition-styles.css">
</head>
<body>
    <div class="writeup-container">
        <header class="writeup-header">
            <h1 class="writeup-title">TJCTF 2024 - Alchemist Recipe Writeup</h1>
            <div class="challenge-meta">
                <div class="meta-item"><span class="meta-label">Category:</span> Crypto</div>
<div class="meta-item"><span class="meta-label">Points:</span> 331</div>
<div class="meta-item"><span class="meta-label">Solves:</span> 59</div>
<div class="meta-item"><span class="meta-label">Author:</span> 2027aliu</div>
                <div class="meta-item"><span class="meta-label">Team:</span> TRIADA</div>
            </div>
        </header>

        <main class="writeup-content">
            <div class="toc">
                <h3>Table of Contents</h3>
                <ul>
                    <li><a href="#challenge">Challenge Description</a></li>
                    <li><a href="#analysis">Analysis</a></li>
                    <li><a href="#solution">Solution</a></li>
                    <li><a href="#flag">Flag</a></li>
                </ul>
            </div>

            
<strong>Category:</strong> Crypto  
<strong>Points:</strong> 331  
<strong>Solves:</strong> 59  
<strong>Author:</strong> 2027aliu

<h2>Challenge Description</h2>

<p>An alchemist claims to have a recipe to transform lead into gold. However, he accidentally encrypted it with a peculiar process of his own. He left behind his notes on the encryption method and an encrypted sample. Unfortunately, he spilled some magic ink on the notes, making them weirdly formatted. The notes include comments showing how he encrypted his recipe. Can you find his "golden" secret?</p>

<strong>Files provided:</strong>
<ul>
<li><code>chall.py</code> - The encryption implementation</li>
<li><code>encrypted.txt</code> - The encrypted flag</li>
</ul>

<h2>Initial Analysis</h2>

<p>Looking at the challenge files, we have:</p>

<ol>
<li><strong>chall.py</strong>: Contains the encryption algorithm with obfuscated variable names</li>
<li><strong>encrypted.txt</strong>: Contains the hex-encoded encrypted flag</li>
</ol>

<p>The "weirdly formatted" notes refer to the intentionally obfuscated variable names in the Python code, making it harder to understand the algorithm at first glance.</p>

<h2>Code Analysis</h2>

<h3>Constants and Key Material</h3>

<div class="code-block"><pre><code class="language-python">SNEEZE_FORK = &quot;AurumPotabileEtChymicumSecretum&quot;  # The encryption key
<p>WUMBLE_BAG = 8  # Block size (8 bytes)</code></pre></div></p>

<p>The key <code>"AurumPotabileEtChymicumSecretum"</code> is Latin meaning "Drinkable Gold and Chemical Secret" - fitting for an alchemist's recipe!</p>

<h3>Key Derivation Function: <code>glorbulate_sprockets_for_bamboozle()</code></h3>

<p>This function takes the key and derives three components using SHA256:</p>

<div class="code-block"><pre><code class="language-python">def glorbulate_sprockets_for_bamboozle(blorbo):
    zing = {}
    yarp = hashlib.sha256(blorbo.encode()).digest()  # 32-byte hash
    zing[&#x27;flibber&#x27;] = list(yarp[:WUMBLE_BAG])        # First 8 bytes
    zing[&#x27;twizzle&#x27;] = list(yarp[WUMBLE_BAG:WUMBLE_BAG+16])  # Next 16 bytes
    glimbo = list(yarp[WUMBLE_BAG+16:])              # Remaining 8 bytes
    
    # Create substitution table using remaining bytes
    snorb = list(range(256))  # Identity permutation [0,1,2,...,255]
    sploop = 0
    for _ in range(256): 
        for z in glimbo:
            wob = (sploop + z) % 256
            snorb[sploop], snorb[wob] = snorb[wob], snorb[sploop]  # Swap
            sploop = (sploop + 1) % 256
    zing[&#x27;drizzle&#x27;] = snorb
<p>    return zing</code></pre></div></p>

<strong>Key components:</strong>
<ul>
<li><code>flibber</code>: First 8 bytes - used for final permutation ordering</li>
<li><code>twizzle</code>: Next 16 bytes - used for XOR operation</li>
<li><code>drizzle</code>: Substitution table created from remaining 8 bytes</li>
</ul>

<h3>Block Encryption: <code>scrungle_crank()</code></h3>

<p>This function encrypts a single 8-byte block:</p>

<div class="code-block"><pre><code class="language-python">def scrungle_crank(dingus, sprockets):
    # Step 1: Apply substitution using drizzle table
    zonked = bytes([sprockets[&#x27;drizzle&#x27;][x] for x in dingus])
    
    # Step 2: XOR with twizzle (cycling through 16 bytes)
    quix = sprockets[&#x27;twizzle&#x27;]
    splatted = bytes([zonked[i] ^ quix[i % len(quix)] for i in range(WUMBLE_BAG)])
    
    # Step 3: Reorder bytes based on sorted flibber values
    wiggle = sprockets[&#x27;flibber&#x27;] 
    waggly = sorted([(wiggle[i], i) for i in range(WUMBLE_BAG)])  # Sort by value
    zort = [oof for _, oof in waggly]  # Extract indices in sorted order
    plunk = [0] * WUMBLE_BAG
    for y in range(WUMBLE_BAG):
        x = zort[y]
        plunk[y] = splatted[x]  # Reorder according to sorted indices
<p>    return bytes(plunk)</code></pre></div></p>

<strong>Encryption steps per block:</strong>
<ol>
<li><strong>Substitution</strong>: Each byte is replaced using the <code>drizzle</code> lookup table</li>
<li><strong>XOR</strong>: Result is XORed with <code>twizzle</code> bytes (cycling through 16 bytes)</li>
<li><strong>Permutation</strong>: Bytes are reordered based on the sorted order of <code>flibber</code> values</li>
</ol>

<h3>Full Encryption: <code>snizzle_bytegum()</code></h3>

<p>This function handles padding and processes the entire message:</p>

<div class="code-block"><pre><code class="language-python">def snizzle_bytegum(bubbles, jellybean):
    # PKCS#7 style padding
    fuzz = WUMBLE_BAG - (len(bubbles) % WUMBLE_BAG)
    if fuzz == 0: 
        fuzz = WUMBLE_BAG
    bubbles += bytes([fuzz] * fuzz)
    
    # Process in 8-byte blocks
    glomp = b&quot;&quot;
    for b in range(0, len(bubbles), WUMBLE_BAG):
        splinter = bubbles[b:b+WUMBLE_BAG]
        zap = scrungle_crank(splinter, jellybean)
        glomp += zap
<p>    return glomp</code></pre></div></p>

<h2>Decryption Strategy</h2>

<p>To decrypt, we need to reverse each step of the encryption process in the correct order:</p>

<h3>1. Reverse Block Decryption</h3>

<p>For each 8-byte encrypted block, reverse the three steps:</p>

<div class="code-block"><pre><code class="language-python">def reverse_scrungle_crank(encrypted_block, sprockets):
    # Step 1: Reverse the final reordering
    wiggle = sprockets[&#x27;flibber&#x27;]
    waggly = sorted([(wiggle[i], i) for i in range(WUMBLE_BAG)])
    zort = [oof for _, oof in waggly]
    
    splatted = [0] * WUMBLE_BAG
    for y in range(WUMBLE_BAG):
        splatted[zort[y]] = encrypted_block[y]  # Reverse: splatted[zort[y]] = plunk[y]
    splatted = bytes(splatted)

    # Step 2: Undo XOR with twizzle
    quix = sprockets[&#x27;twizzle&#x27;]
    zonked = bytes([splatted[i] ^ quix[i % len(quix)] for i in range(WUMBLE_BAG)])

    # Step 3: Reverse the drizzle substitution
    drizzle = sprockets[&#x27;drizzle&#x27;]
    reverse_drizzle = [0] * 256
    for i in range(256):
        reverse_drizzle[drizzle[i]] = i  # Create inverse lookup table

    original = bytes([reverse_drizzle[x] for x in zonked])
<p>    return original</code></pre></div></p>

<h3>2. Full Decryption with Padding Removal</h3>

<div class="code-block"><pre><code class="language-python">def unsnizzle_bytegum(encrypted_data, jellybean):
    decrypted = b&quot;&quot;
    for b in range(0, len(encrypted_data), WUMBLE_BAG):
        encrypted_block = encrypted_data[b:b+WUMBLE_BAG]
        decrypted_block = reverse_scrungle_crank(encrypted_block, jellybean)
        decrypted += decrypted_block
    
    # Remove PKCS#7 padding
    if decrypted:
        padding_length = decrypted[-1]
        if padding_length &lt;= WUMBLE_BAG and all(b == padding_length for b in decrypted[-padding_length:]):
            decrypted = decrypted[:-padding_length]
    
<p>    return decrypted</code></pre></div></p>

<h2>Solution Implementation</h2>

<p>The complete decryption script:</p>

<div class="code-block"><pre><code class="language-python">def decrypt_recipe():
    # Read encrypted data
    with open(&quot;encrypted.txt&quot;, &quot;r&quot;) as f:
        encrypted_hex = f.read().strip()
    
    encrypted_data = bytes.fromhex(encrypted_hex)
    
    # Generate the same key material used for encryption
    jellybean = glorbulate_sprockets_for_bamboozle(SNEEZE_FORK)
    
    # Decrypt
    decrypted = unsnizzle_bytegum(encrypted_data, jellybean)
    
    print(f&quot;Decrypted recipe: {decrypted.decode()}&quot;)
<p>    return decrypted</code></pre></div></p>

<h2>Key Insights</h2>

<ol>
<li><strong>Obfuscation vs Security</strong>: The challenge uses silly variable names to make the code harder to read, but the underlying algorithm is a straightforward custom block cipher.</li>
</ol>

<ol>
<li><strong>Reversible Operations</strong>: Each step in the encryption is carefully designed to be reversible:</li>
<ul>
</ol>
<li>Substitution tables can be inverted</li>
<li>XOR is its own inverse</li>
<li>Permutations can be undone by tracking the reordering</li>
</ul>

<ol>
<li><strong>Block Cipher Structure</strong>: This follows a classic block cipher pattern with substitution, XOR (like a stream cipher component), and permutation.</li>
</ol>

<ol>
<li><strong>Deterministic Key Derivation</strong>: Using SHA256 ensures the same key always produces the same derived components.</li>
</ol>

<h2>Running the Solution</h2>

<div class="code-block"><pre><code class="language-bash">python3 chall.py</code></pre></div>

Output:
<div class="code-block"><pre><code class="language-text">Testing encryption/decryption:
Test data: b&#x27;tjctf{test_flag_12345678}&#x27;
Encrypted: b84254d7b5920901522eaf847ecb370771782ceb85cec9f2ad13c34943fe6553
Decrypted: b&#x27;tjctf{test_flag_12345678}&#x27;
Match: True

Decrypting the actual challenge:
<p>Decrypted recipe: tjctf{thank_you_for_making_me_normal_again_yay}</code></pre></div></p>

<h2>Flag</h2>

<strong><code>tjctf{thank_you_for_making_me_normal_again_yay}</code></strong>

<p>The flag message humorously refers to "making me normal again" - likely referring to the process of un-obfuscating the weird variable names and understanding the algorithm!</p>

<h2>Complete Solution Code</h2>

<p>Here's the full working decryption implementation added to the original <code>chall.py</code>:</p>

<div class="code-block"><pre><code class="language-python">def reverse_scrungle_crank(encrypted_block, sprockets):
    &quot;&quot;&quot;Reverse the scrungle_crank operation&quot;&quot;&quot;
    if len(encrypted_block) != WUMBLE_BAG:
        raise ValueError(f&quot;Must be {WUMBLE_BAG} wumps for crankshaft.&quot;)

    # Step 1: Reverse the final reordering step (lines 28-34 in original)
    # The original does: plunk[y] = splatted[zort[y]]
    # So to reverse: splatted[zort[y]] = plunk[y]
    wiggle = sprockets[&#x27;flibber&#x27;]
    waggly = sorted([(wiggle[i], i) for i in range(WUMBLE_BAG)])
    zort = [oof for _, oof in waggly]

    splatted = [0] * WUMBLE_BAG
    for y in range(WUMBLE_BAG):
        splatted[zort[y]] = encrypted_block[y]
    splatted = bytes(splatted)

    # Step 2: Undo XOR with twizzle (line 27 in original)
    quix = sprockets[&#x27;twizzle&#x27;]
    zonked = bytes([splatted[i] ^ quix[i % len(quix)] for i in range(WUMBLE_BAG)])

    # Step 3: Reverse the drizzle permutation (line 25 in original)
    # The original does: zonked[i] = drizzle[dingus[i]]
    # So to reverse: dingus[i] = reverse_drizzle[zonked[i]]
    drizzle = sprockets[&#x27;drizzle&#x27;]
    reverse_drizzle = [0] * 256
    for i in range(256):
        reverse_drizzle[drizzle[i]] = i

    original = bytes([reverse_drizzle[x] for x in zonked])
    return original

def unsnizzle_bytegum(encrypted_data, jellybean):
    &quot;&quot;&quot;Reverse the snizzle_bytegum operation&quot;&quot;&quot;
    if len(encrypted_data) % WUMBLE_BAG != 0:
        raise ValueError(&quot;Encrypted data length must be multiple of WUMBLE_BAG&quot;)

    decrypted = b&quot;&quot;
    for b in range(0, len(encrypted_data), WUMBLE_BAG):
        encrypted_block = encrypted_data[b:b+WUMBLE_BAG]
        decrypted_block = reverse_scrungle_crank(encrypted_block, jellybean)
        decrypted += decrypted_block

    # Remove padding
    if decrypted:
        padding_length = decrypted[-1]
        if padding_length &lt;= WUMBLE_BAG and all(b == padding_length for b in decrypted[-padding_length:]):
            decrypted = decrypted[:-padding_length]

    return decrypted

def decrypt_recipe():
    &quot;&quot;&quot;Decrypt the recipe from encrypted.txt&quot;&quot;&quot;
    # Read encrypted data
    with open(&quot;encrypted.txt&quot;, &quot;r&quot;) as f:
        encrypted_hex = f.read().strip()

    encrypted_data = bytes.fromhex(encrypted_hex)

    # Generate the same jellybean (key material) used for encryption
    jellybean = glorbulate_sprockets_for_bamboozle(SNEEZE_FORK)

    # Decrypt
    decrypted = unsnizzle_bytegum(encrypted_data, jellybean)

    print(f&quot;Decrypted recipe: {decrypted.decode(errors=&#x27;ignore&#x27;)}&quot;)
    return decrypted

def test_encryption_decryption():
    &quot;&quot;&quot;Test that our decryption correctly reverses encryption&quot;&quot;&quot;
    test_data = b&quot;tjctf{test_flag_12345678}&quot;  # Test with a known flag format

    # Encrypt
    jellybean = glorbulate_sprockets_for_bamboozle(SNEEZE_FORK)
    encrypted = snizzle_bytegum(test_data, jellybean)
    print(f&quot;Test data: {test_data}&quot;)
    print(f&quot;Encrypted: {encrypted.hex()}&quot;)

    # Decrypt
    decrypted = unsnizzle_bytegum(encrypted, jellybean)
    print(f&quot;Decrypted: {decrypted}&quot;)
    print(f&quot;Match: {test_data == decrypted}&quot;)

if __name__ == &quot;__main__&quot;:
    # Test our decryption logic first
    print(&quot;Testing encryption/decryption:&quot;)
    test_encryption_decryption()
    print(&quot;\nDecrypting the actual challenge:&quot;)

    # Run decryption on the challenge
<p>    decrypt_recipe()</code></pre></div></p>




            <div class="flag-section" id="flag">
                <h2>Flag</h2>
                <div class="flag">🚩 tjctf{thank_you_for_making_me_normal_again_yay}</div>
            </div>
        </main>

        <footer class="writeup-footer">
            <p><strong>Team TRIADA</strong> - Competitive CTF Team</p>
            <p>Writeup prepared for competition submission</p>
        </footer>
    </div>
</body>
</html>