# TJCTF 2024 - The Art of War (Crypto)

**Category:** Crypto  
**Points:** 259  
**Solves:** 135  
**Author:** thegreenmallard  

## Challenge Description

> "In the midst of chaos, there is also opportunity"
> 
> <PERSON>, The Art of War

**Files provided:**
- `main.py` - The encryption script
- `output.txt` - The encrypted output

## Initial Analysis

Let's examine the provided files to understand the challenge:

### main.py
```python
from Crypto.Util.number import bytes_to_long, getPrime, long_to_bytes
import time

flag = open('flag.txt', 'rb').read()
m = bytes_to_long(flag)

e = getPrime(8)
print(f'e = {e}')

def generate_key():
    p, q = getPrime(256), getPrime(256)
    while (p - 1) % e == 0:
        p = getPrime(256)
    while (q - 1) % e == 0:
        q = getPrime(256)
    return p * q
    
for i in range(e):
    n = generate_key()
    c = pow(m, e, n)
    print(f'n{i} = {n}')
    print(f'c{i} = {c}')
```

### Key Observations

1. **Small exponent**: `e` is an 8-bit prime (from `getPrime(8)`)
2. **Multiple encryptions**: The same message `m` is encrypted `e` times with different RSA moduli
3. **Same exponent**: All encryptions use the same exponent `e`
4. **Different moduli**: Each encryption uses a freshly generated RSA modulus `n_i = p_i * q_i`

From `output.txt`, we can see:
- `e = 229` (a prime number)
- 229 different RSA moduli (`n0` through `n228`)
- 229 corresponding ciphertexts (`c0` through `c228`)

## Vulnerability: Chinese Remainder Theorem Attack

This setup is vulnerable to a **Chinese Remainder Theorem (CRT) attack** on RSA. Here's why:

### The Mathematical Foundation

We have the system of congruences:
```
m^e ≡ c_0 (mod n_0)
m^e ≡ c_1 (mod n_1)
...
m^e ≡ c_228 (mod n_228)
```

Since:
1. We have exactly `e = 229` congruences
2. All moduli are pairwise coprime (different RSA moduli)
3. The message `m` is likely much smaller than the product of all moduli

We can use the Chinese Remainder Theorem to find `m^e mod (n_0 * n_1 * ... * n_228)`.

### Why This Works

If `m^e < n_0 * n_1 * ... * n_228`, then:
```
m^e ≡ CRT_result (mod ∏n_i)
```

becomes:
```
m^e = CRT_result
```

We can then take the `e`-th root to recover `m`.

## Solution Implementation

Here's the complete solution:

```python
#!/usr/bin/env python3

from Crypto.Util.number import long_to_bytes
import re

def extended_gcd(a, b):
    if a == 0:
        return b, 0, 1
    gcd, x1, y1 = extended_gcd(b % a, a)
    x = y1 - (b // a) * x1
    y = x1
    return gcd, x, y

def chinese_remainder_theorem(remainders, moduli):
    total = 0
    prod = 1
    for m in moduli:
        prod *= m

    for r, m in zip(remainders, moduli):
        p = prod // m
        _, inv, _ = extended_gcd(p, m)
        total += r * inv * p

    return total % prod

def nth_root(x, n):
    if x <= 1:
        return x

    # Newton's method for nth root
    bit_length = x.bit_length()
    guess = 1 << ((bit_length + n - 1) // n)

    prev_guess = 0
    while guess != prev_guess:
        prev_guess = guess
        guess = ((n - 1) * guess + x // (guess ** (n - 1))) // n

    # Verify result
    while guess ** n > x:
        guess -= 1

    return guess

# Parse output.txt
with open('output.txt', 'r') as f:
    content = f.read()

# Extract e, n values, and c values
e = int(re.search(r'e = (\d+)', content).group(1))
n_values = [int(re.search(rf'n{i} = (\d+)', content).group(1)) for i in range(e)]
c_values = [int(re.search(rf'c{i} = (\d+)', content).group(1)) for i in range(e)]

# Apply CRT to solve m^e ≡ c_i (mod n_i)
m_to_e = chinese_remainder_theorem(c_values, n_values)

# Take eth root to get m
m = nth_root(m_to_e, e)

# Convert to flag
flag = long_to_bytes(m)
print(flag.decode())
```

## Execution and Result

Running the solution:

```bash
$ python3 decode.py
tjctf{the_greatest_victory_is_that_which_require_no_battle}
```

## Flag

```
tjctf{the_greatest_victory_is_that_which_require_no_battle}
```

## Key Insights

1. **Theme Connection**: The flag is a quote from Sun Tzu's "The Art of War": "The greatest victory is that which requires no battle" - fitting the challenge theme perfectly.

2. **Attack Efficiency**: This attack demonstrates how using the same small exponent across multiple RSA instances can be catastrophic, even when each individual RSA key is secure.

3. **Mathematical Beauty**: The Chinese Remainder Theorem provides an elegant solution to what initially appears to be an intractable problem.

## Lessons Learned

- **Never reuse small exponents** across multiple RSA encryptions of the same message
- **Small exponents** in RSA can be dangerous when combined with poor implementation practices
- **Mathematical attacks** often provide the most elegant solutions to cryptographic challenges

This challenge beautifully demonstrates how mathematical theory (CRT) can be applied to break seemingly secure cryptographic implementations through careful analysis of the underlying mathematical structure.
