<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TJCTF 2024 - Close Secrets Writeup - TRIADA CTF Writeup</title>
    <link rel="stylesheet" href="competition-styles.css">
</head>
<body>
    <div class="writeup-container">
        <header class="writeup-header">
            <h1 class="writeup-title">TJCTF 2024 - Close Secrets Writeup</h1>
            <div class="challenge-meta">
                
                <div class="meta-item"><span class="meta-label">Team:</span> TRIADA</div>
            </div>
        </header>

        <main class="writeup-content">
            <div class="toc">
                <h3>Table of Contents</h3>
                <ul>
                    <li><a href="#challenge">Challenge Description</a></li>
                    <li><a href="#analysis">Analysis</a></li>
                    <li><a href="#solution">Solution</a></li>
                    <li><a href="#flag">Flag</a></li>
                </ul>
            </div>

            
<strong>Challenge</strong>: Close Secrets  
<strong>Category</strong>: Crypto  
<strong>Points</strong>: 326  
<strong>Solves</strong>: 107  

<h2>Challenge Description</h2>

<blockquote>I tried to make my Diffie-Hellman implementation a bit more interesting, but maybe I went too far?</blockquote>
<p>> </p>
<blockquote>Can you make sense of this custom cryptography and decode the encrypted flag?</blockquote>

<strong>Files provided:</strong>
<ul>
<li><code>params.txt</code> - Contains the Diffie-Hellman parameters</li>
<li><code>encrypt.py</code> - The encryption implementation</li>
<li><code>enc_flag</code> - The encrypted flag</li>
</ul>

<h2>Understanding the Challenge</h2>

<h3>What is Diffie-Hellman?</h3>

<p>Diffie-Hellman is a method for two parties to agree on a shared secret key over an insecure channel. Here's how it normally works:</p>

<ol>
<li><strong>Public parameters</strong>: Everyone knows <code>p</code> (a large prime) and <code>g</code> (a generator)</li>
<li><strong>Private keys</strong>: Alice picks secret <code>a</code>, Bob picks secret <code>b</code></li>
<li><strong>Public keys</strong>: Alice computes <code>A = g^a mod p</code>, Bob computes <code>B = g^b mod p</code></li>
<li><strong>Shared secret</strong>: Alice computes <code>B^a mod p</code>, Bob computes <code>A^b mod p</code></li>
<li><strong>Magic</strong>: Both get the same result: <code>g^(ab) mod p</code></li>
</ol>

<p>The security relies on the fact that even if you know <code>g</code>, <code>p</code>, <code>A</code>, and <code>B</code>, it's very hard to figure out <code>a</code> or <code>b</code> (this is called the discrete logarithm problem).</p>

<h3>Analyzing the Code</h3>

<p>Let's look at the key generation function in <code>encrypt.py</code>:</p>

<div class="code-block"><pre><code class="language-python">def generate_dh_key():
    p = number.getPrime(1024)  # Generate a 1024-bit prime
    g = number.getPrime(1024)  # Generate another 1024-bit prime
    a = randint(p - 10, p)     # 🚨 VULNERABILITY: Only 10 possible values!
    b = randint(g - 10, g)     # 🚨 VULNERABILITY: Only 10 possible values!
    u = pow(g, a, p)           # u = g^a mod p
    v = pow(g, b, p)           # v = g^b mod p
    key = pow(v, a, p)         # shared key = v^a mod p
    b_key = pow(u, b, p)       # verification = u^b mod p
    if key != b_key:
        sys.exit(1)
<p>    return p, g, u, v, key</code></pre></div></p>

<strong>The Critical Flaw</strong>: Instead of choosing <code>a</code> and <code>b</code> from the full range <code>[1, p-1]</code> (which would have ~2^1024 possibilities), the code only chooses from tiny ranges:
<ul>
<li><code>a</code> can only be one of: <code>p-10, p-9, p-8, p-7, p-6, p-5, p-4, p-3, p-2, p-1</code> (10 values)</li>
<li><code>b</code> can only be one of: <code>g-10, g-9, g-8, g-7, g-6, g-5, g-4, g-3, g-2, g-1</code> (10 values)</li>
</ul>

<p>This means there are only <strong>10 × 10 = 100</strong> possible combinations to try!</p>

<h3>Understanding the Encryption Process</h3>

<p>The flag is encrypted using a two-step process:</p>

<ol>
<li><strong>XOR Encryption</strong> (<code>dynamic_xor_encrypt</code>):</li>
<ul>
</ol>
<li>Creates a key by hashing the shared secret: <code>SHA256(shared_key)</code></li>
<li>XORs each byte of the <strong>reversed</strong> flag with the key bytes</li>
</ul>
   
<ol>
<li><strong>Outer Encryption</strong> (<code>encrypt_outer</code>):</li>
<ul>
</ol>
<li>For each byte value, applies: <code>(value + key%256) * key</code></li>
</ul>

<h2>The Attack Strategy</h2>

<p>Since there are only 100 possible key combinations, we can try them all:</p>

<ol>
<li><strong>Brute force the private keys</strong>: Try all combinations of <code>a ∈ [p-10, p-1]</code> and <code>b ∈ [g-10, g-1]</code></li>
<li><strong>Check if valid</strong>: For each combination, compute <code>u = g^a mod p</code> and <code>v = g^b mod p</code></li>
<li><strong>Match with given values</strong>: If our computed <code>u</code> and <code>v</code> match the ones in <code>params.txt</code>, we found the right keys!</li>
<li><strong>Compute shared secret</strong>: Calculate <code>shared_key = v^a mod p</code></li>
<li><strong>Decrypt the flag</strong>: Reverse the encryption process</li>
</ol>

<h2>Solution Implementation</h2>

<p>Here's the complete solution:</p>

<div class="code-block"><pre><code class="language-python">#!/usr/bin/env python3
import hashlib

def decrypt_outer(cipher_ords, key):
    &quot;&quot;&quot;Reverse the outer encryption: (val + key_offset) * key&quot;&quot;&quot;
    plaintext = []
    key_offset = key % 256
    for val in cipher_ords:
        # Reverse: original = (val / key) - key_offset
        original = (val // key) - key_offset
        plaintext.append(original)
    return plaintext

def dynamic_xor_decrypt(encrypted_ords, text_key_bytes):
    &quot;&quot;&quot;Reverse the XOR encryption and un-reverse the bytes&quot;&quot;&quot;
    decrypted_bytes = []
    key_length = len(text_key_bytes)
    for i, encrypted_ord in enumerate(encrypted_ords):
        key_byte = text_key_bytes[i % key_length]
        decrypted_bytes.append(encrypted_ord ^ key_byte)
    # Un-reverse the bytes (they were reversed during encryption)
    return bytes(decrypted_bytes[::-1])

def solve_challenge():
    # Read the Diffie-Hellman parameters
    with open(&quot;params.txt&quot;, &quot;r&quot;) as f:
        lines = f.readlines()
    
    p = int(lines[0].split(&quot; = &quot;)[1].strip())
    g = int(lines[1].split(&quot; = &quot;)[1].strip())
    u = int(lines[2].split(&quot; = &quot;)[1].strip())
    v = int(lines[3].split(&quot; = &quot;)[1].strip())
    
    # Read the encrypted flag
    with open(&quot;enc_flag&quot;, &quot;r&quot;) as f:
        enc_flag_str = f.read().strip()
    final_cipher = eval(enc_flag_str)  # Parse the list
    
    print(&quot;Brute forcing the private keys...&quot;)
    
    # Try all possible values of a and b
    for a in range(p - 10, p):      # 10 possible values for a
        for b in range(g - 10, g):  # 10 possible values for b
            try:
                # Calculate what u and v should be with these a, b values
                expected_u = pow(g, a, p)
                expected_v = pow(g, b, p)
                
                # Check if this matches the given u, v
                if expected_u == u and expected_v == v:
                    print(f&quot;Found the private keys!&quot;)
                    
                    # Calculate the shared secret
                    shared_key = pow(v, a, p)
                    
                    # Generate the XOR key (same as in encryption)
                    xor_key_str = hashlib.sha256(str(shared_key).encode()).hexdigest()
                    xor_key_bytes = xor_key_str.encode(&#x27;utf-8&#x27;)
                    
                    # Decrypt step by step
                    intermediate_ords = decrypt_outer(final_cipher, shared_key)
                    flag_bytes = dynamic_xor_decrypt(intermediate_ords, xor_key_bytes)
                    flag = flag_bytes.decode(&#x27;utf-8&#x27;)
                    
                    print(f&quot;Decrypted flag: {flag}&quot;)
                    return flag
                    
            except Exception:
                continue  # Try next combination
    
    print(&quot;No valid solution found&quot;)
    return None

if __name__ == &quot;__main__&quot;:
<p>    solve_challenge()</code></pre></div></p>

<h2>Running the Solution</h2>

<div class="code-block"><pre><code class="language-bash">$ python3 solve.py
Brute forcing the private keys...
Found the private keys!
<p>Decrypted flag: tjctf{sm4ll_r4ng3_sh0rt_s3cr3t}</code></pre></div></p>

<h2>Key Takeaways</h2>

<ol>
<li><strong>Cryptographic implementations must use proper key sizes</strong>: The private keys should be chosen from the full range <code>[1, p-1]</code>, not a tiny subset.</li>
</ol>

<ol>
<li><strong>Small key spaces are vulnerable to brute force</strong>: 100 combinations is trivial for a computer to try.</li>
</ol>

<ol>
<li><strong>The flag name is a hint</strong>: <code>sm4ll_r4ng3_sh0rt_s3cr3t</code> directly references the vulnerability - small range leading to short (weak) secrets.</li>
</ol>

<ol>
<li><strong>Defense</strong>: Always use cryptographically secure random number generation with appropriate ranges for your key material.</li>
</ol>

<p>This challenge demonstrates why proper implementation of cryptographic algorithms is crucial - even a small mistake can completely break the security!</p>

<h2>Step-by-Step Walkthrough for Beginners</h2>

<h3>Step 1: Examine the Files</h3>

<p>First, let's look at what we have:</p>

<strong>params.txt</strong> contains the public Diffie-Hellman parameters:
<div class="code-block"><pre><code class="language-text">p = 170414318260705733587875759581770955570309017044222937085191604024452735032594248474424765923836540211737893584439554899006773968654769091193247412851040473946419617178998073469868100539248246324253541060008089222137232131934348865470429102329705344623611275490470993627132873486693712828328643742288205112527
g = 120681139203436291164378184116247954042577742288954642499192807256993763587666398677530059224138614478338902785098981693557339976850974900098504529593380063851724924557945231653341683157340782295208957489088101760434538987711049950242033559644930710185044163047334693996198198372552023208414727714764489712283
u = 135668771029510263907110237169555858182389140035220525526216692019541532644619777145586283806534328053310689073913522508004464538889367992552446578372649985255898549272854575377510712592134690407014502401681964878932616905735136520060908211096236619268316849967775899568661131681489146190493710890951160724706
<p>v = 165036785985552337550703112630578321968495250714033243796585786662838937148680751454783550779786426910035205483848329274328823513229605340921993107676999077541506833174273521350152646080628330616219078232643906110969196674275384683101159565935895506360072481873027262155137472248477311708158177572618168100749</code></pre></div></p>

<strong>enc_flag</strong> contains a long list of numbers - this is our encrypted flag.

<h3>Step 2: Understand the Vulnerability</h3>

<p>The key insight is in this part of the code:</p>
<div class="code-block"><pre><code class="language-python">a = randint(p - 10, p)  # Should be randint(1, p-1)
<p>b = randint(g - 10, g)  # Should be randint(1, g-1)</code></pre></div></p>

<p>In proper Diffie-Hellman, <code>a</code> and <code>b</code> should be random numbers between 1 and p-1. But here, they're only chosen from the last 10 numbers before p and g respectively.</p>

<h3>Step 3: The Brute Force Attack</h3>

<p>Since there are only 10 × 10 = 100 combinations, we can try them all:</p>

<div class="code-block"><pre><code class="language-python">for a in range(p - 10, p):      # Try a = p-10, p-9, ..., p-1
    for b in range(g - 10, g):  # Try b = g-10, g-9, ..., g-1
        # Check if this combination gives us the right u and v
        if pow(g, a, p) == u and pow(g, b, p) == v:
            # Found it! Now we can calculate the shared key
<p>            shared_key = pow(v, a, p)</code></pre></div></p>

<h3>Step 4: Reverse the Encryption</h3>

<p>Once we have the shared key, we need to undo the encryption:</p>

<ol>
<li><strong>Undo outer encryption</strong>: Each number was transformed by <code>(original + offset) * key</code></li>
<li><strong>Undo XOR encryption</strong>: XOR with the key again and reverse the byte order</li>
</ol>

<h3>Step 5: Get the Flag</h3>

<p>Run the solution and get: <code>tjctf{sm4ll_r4ng3_sh0rt_s3cr3t}</code></p>

<h2>Why This Attack Works</h2>

<ul>
<li><strong>Normal Diffie-Hellman</strong>: ~2^1024 possible private keys (impossible to brute force)</li>
<li><strong>This implementation</strong>: Only 100 possible combinations (trivial to brute force)</li>
<li><strong>Time complexity</strong>: O(100) instead of O(2^1024)</li>
</ul>

<p>The lesson: In cryptography, implementation details matter enormously!</p>


            <div class="flag-section" id="flag">
                <h2>Flag</h2>
                <div class="flag">🚩 tjctf{sm4ll_r4ng3_sh0rt_s3cr3t}</div>
            </div>
        </main>

        <footer class="writeup-footer">
            <p><strong>Team TRIADA</strong> - Competitive CTF Team</p>
            <p>Writeup prepared for competition submission</p>
        </footer>
    </div>
</body>
</html>