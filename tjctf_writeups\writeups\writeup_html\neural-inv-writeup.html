<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neural Network Inversion Challenge - TJCTF 2024 - TRIADA CTF Writeup</title>
    <link rel="stylesheet" href="writeup-styles.css">
</head>
<body>
    <nav class="writeup-nav">
        <a href="../../../index.html" class="back-btn">← Back to Home</a>
    </nav>
    
    <div class="writeup-container">
        <header class="writeup-header">
            <h1 class="writeup-title">Neural Network Inversion Challenge - TJCTF 2024</h1>
            <div class="challenge-meta">
                <div class="meta-item"><span class="meta-label">Category:</span> Reverse Engineering</div>
<div class="meta-item"><span class="meta-label">Points:</span> 430</div>
<div class="meta-item"><span class="meta-label">Solves:</span> 77</div>
            </div>
        </header>
        
        <main class="writeup-content">
            
<h2>Challenge Overview</h2>

<strong>Challenge Name:</strong> neural-inversion  
<strong>Category:</strong> Reverse Engineering  
<strong>Points:</strong> 430  
<strong>Solves:</strong> 77  

<strong>Description:</strong>  
<p>Use the provided model.npz to recover the hidden ASCII flag by algebraically inverting the network's layers.</p>

<strong>Files Provided:</strong>
<ul>
<li><code>model.npz</code> - Neural network model containing weights, biases, and target output</li>
</ul>

<h2>Understanding the Challenge</h2>

<p>This challenge involves <strong>neural network inversion</strong> - the process of working backwards through a trained neural network to recover the original input that produced a given output. The flag is encoded as the input to a 2-layer neural network, and we need to mathematically reverse the network's computations.</p>

<h2>Step 1: Analyzing the Neural Network</h2>

<p>First, let's examine what's inside the model file:</p>

<pre><code class="language-python">import numpy as np

<h1>Load the model</h1>
model = np.load(&quot;model.npz&quot;)
print(&quot;Keys in model:&quot;, list(model.keys()))

<h1>Examine each component</h1>
for key in model.keys():
    data = model[key]
<p>    print(f&quot;{key}: shape {data.shape}&quot;)</code></pre></p>

<strong>Output:</strong>
<pre><code class="language-text">Keys in model: [&#x27;W1&#x27;, &#x27;b1&#x27;, &#x27;W2&#x27;, &#x27;b2&#x27;, &#x27;y&#x27;]
W1: shape (30, 30)  # First layer weights
b1: shape (30,)    # First layer biases  
W2: shape (30, 30)  # Second layer weights
b2: shape (30,)    # Second layer biases
<p>y: shape (30,)     # Target output</code></pre></p>

<h2>Step 2: Understanding the Network Architecture</h2>

<p>The neural network has this structure:</p>
<pre><code class="language-text">Input (x) → [W1, b1] → Activation → [W2, b2] → Activation → Output (y)
<p>    30         30                      30                      30</code></pre></p>

<p>The mathematical operations are:</p>
<ol>
<li><code>z1 = W1 @ x + b1</code> (linear transformation)</li>
<li><code>h1 = activation(z1)</code> (activation function)</li>
<li><code>z2 = W2 @ h1 + b2</code> (linear transformation)</li>
<li><code>y = activation(z2)</code> (activation function)</li>
</ol>

<h2>Step 3: Determining the Activation Function</h2>

<p>Looking at the target output <code>y</code>, all values are between 0 and 1:</p>
<pre><code class="language-python">print(f&quot;y range: [{np.min(y):.6f}, {np.max(y):.6f}]&quot;)
<h1>Output: y range: [0.001663, 0.998565]</code></pre></h1>

<p>This strongly suggests <strong>sigmoid activation</strong> (not ReLU), since:</p>
<ul>
<li>Sigmoid outputs are always in range (0, 1)</li>
<li>ReLU outputs can be any positive value</li>
</ul>

<p>The sigmoid function is: <code>σ(x) = 1 / (1 + e^(-x))</code></p>

<h2>Step 4: Mathematical Inversion Process</h2>

<p>To recover the input <code>x</code>, we need to work backwards through each layer:</p>

<h3>Step 4.1: Invert the Final Sigmoid</h3>
<p>Given: <code>y = σ(z2)</code>  </p>
<p>We need: <code>z2 = σ^(-1)(y)</code></p>

<p>The inverse sigmoid function is:</p>
<pre><code class="language-python">def sigmoid_inverse(y):
    y = np.clip(y, 1e-15, 1-1e-15)  # Avoid numerical issues
    return np.log(y / (1 - y))

<p>z2 = sigmoid_inverse(y)</code></pre></p>

<h3>Step 4.2: Solve for Hidden Layer</h3>
<p>Given: <code>z2 = W2 @ h1 + b2</code>  </p>
<p>We need: <code>h1 = W2^(-1) @ (z2 - b2)</code></p>

<pre><code class="language-python">h1 = np.linalg.inv(W2) @ (z2 - b2)</code></pre>

<h3>Step 4.3: Invert the Hidden Sigmoid  </h3>
Given: <code>h1 = σ(z1)</code>  
We need: <code>z1 = σ^(-1)(h1)</code>

<pre><code class="language-python">h1_clipped = np.clip(h1, 1e-15, 1-1e-15)  # Ensure valid range
<p>z1 = sigmoid_inverse(h1_clipped)</code></pre></p>

<h3>Step 4.4: Solve for Input</h3>
<p>Given: <code>z1 = W1 @ x + b1</code>  </p>
<p>We need: <code>x = W1^(-1) @ (z1 - b1)</code></p>

<pre><code class="language-python">x = np.linalg.inv(W1) @ (z1 - b1)</code></pre>

<h2>Step 5: Complete Solution Code</h2>

<pre><code class="language-python">#!/usr/bin/env python3
import numpy as np

def sigmoid_inverse(y):
    &quot;&quot;&quot;Inverse of sigmoid function&quot;&quot;&quot;
    y = np.clip(y, 1e-15, 1-1e-15)
    return np.log(y / (1 - y))

def invert_neural_network():
    &quot;&quot;&quot;Invert 2-layer sigmoid neural network to recover input&quot;&quot;&quot;
    # Load model
    model = np.load(&quot;model.npz&quot;)
    W1, b1, W2, b2, y = model[&quot;W1&quot;], model[&quot;b1&quot;], model[&quot;W2&quot;], model[&quot;b2&quot;], model[&quot;y&quot;]
    
    # Step 1: Invert final sigmoid layer
    z2 = sigmoid_inverse(y)
    
    # Step 2: Solve for hidden layer: h1 = W2^(-1) @ (z2 - b2)
    h1 = np.linalg.inv(W2) @ (z2 - b2)
    
    # Step 3: Invert hidden sigmoid layer
    h1_clipped = np.clip(h1, 1e-15, 1-1e-15)
    z1 = sigmoid_inverse(h1_clipped)
    
    # Step 4: Solve for input: x = W1^(-1) @ (z1 - b1)
    x = np.linalg.inv(W1) @ (z1 - b1)
    
    return x

def extract_flag(x):
    &quot;&quot;&quot;Extract ASCII flag from recovered input&quot;&quot;&quot;
    # Scale by 127 and convert to ASCII
    ascii_vals = np.round(x * 127).astype(int)
    flag = &#x27;&#x27;.join([chr(c) for c in ascii_vals])
    return flag

if __name__ == &quot;__main__&quot;:
    # Invert the neural network
    x = invert_neural_network()
    
    # Extract the flag
    flag = extract_flag(x)
<p>    print(f&quot;Flag: {flag}&quot;)</code></pre></p>

<h2>Step 6: Flag Extraction</h2>

<p>The recovered input <code>x</code> contains values in the range [0.377953, 0.984252]. To convert these to ASCII characters:</p>

<ol>
<li><strong>Scale by 127:</strong> <code>ascii_vals = round(x * 127)</code></li>
<li><strong>Convert to characters:</strong> <code>chr(ascii_val)</code> for each value</li>
</ol>

<p>The scaling factor of 127 was discovered through experimentation with different scales (1, 10, 100, 127, 255) until readable text appeared.</p>

<h2>Final Result</h2>

<strong>Flag:</strong> <code>tjctf{m0d3l_1nv3rs10n_f9a93qw}</code>

<h2>Key Learning Points</h2>

<ol>
<li><strong>Neural Network Inversion:</strong> This technique can recover inputs from outputs when you have access to the model weights</li>
<li><strong>Activation Function Identification:</strong> Output ranges give clues about activation functions</li>
<li><strong>Mathematical Precision:</strong> Numerical stability is crucial when inverting functions</li>
<li><strong>Matrix Inversion:</strong> Linear algebra operations are fundamental to neural network inversion</li>
<li><strong>Data Encoding:</strong> Flags can be encoded in various ways - here as normalized ASCII values</li>
</ol>

<h2>Why This Works</h2>

<p>Neural network inversion is possible here because:</p>
<ul>
<li>The network is relatively simple (2 layers)</li>
<li>All weight matrices are invertible (square and full rank)</li>
<li>We have exact target outputs (no noise)</li>
<li>The activation function (sigmoid) has a well-defined inverse</li>
</ul>

<p>In practice, this technique is used in:</p>
<ul>
<li>Adversarial attacks on neural networks</li>
<li>Privacy analysis of machine learning models</li>
<li>Understanding what neural networks have learned</li>
</ul>

<h2>Alternative Approaches</h2>

<p>If the direct inversion hadn't worked, other approaches could include:</p>
<ul>
<li><strong>Gradient-based optimization:</strong> Use gradient descent to find input that produces target output</li>
<li><strong>Brute force search:</strong> Try different input combinations (computationally expensive)</li>
<li><strong>Genetic algorithms:</strong> Evolutionary approach to find the input</li>
</ul>

<p>This challenge demonstrates the importance of understanding both the mathematical foundations of neural networks and potential security implications when model weights are exposed.</p>

            <div class="flag">🚩 tjctf{m0d3l_1nv3rs10n_f9a93qw}</div>
        </main>
    </div>
</body>
</html>