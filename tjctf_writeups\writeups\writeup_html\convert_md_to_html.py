#!/usr/bin/env python3
"""
Convert markdown writeups to HTML with custom styling
"""

import os
import re
import html
from pathlib import Path

def parse_markdown_metadata(content):
    """Extract metadata from markdown content"""
    lines = content.split('\n')
    title = ""
    category = ""
    points = ""
    solves = ""
    author = ""
    
    # Find title (first # heading)
    for line in lines:
        if line.startswith('# '):
            title = line[2:].strip()
            break
    
    # Find metadata in the first few lines
    for line in lines[:10]:
        if '**Category:**' in line:
            category = line.split('**Category:**')[1].strip()
        elif '**Points:**' in line:
            points = line.split('**Points:**')[1].strip()
        elif '**Solves:**' in line:
            solves = line.split('**Solves:**')[1].strip()
        elif '**Author:**' in line:
            author = line.split('**Author:**')[1].strip()
    
    return {
        'title': title,
        'category': category,
        'points': points,
        'solves': solves,
        'author': author
    }

def convert_markdown_to_html(md_content):
    """Convert markdown content to HTML"""
    html_content = md_content

    # First, handle code blocks to prevent interference with other conversions
    html_content = re.sub(
        r'```(\w+)?\n(.*?)\n```',
        lambda m: f'<pre><code class="language-{m.group(1) or "text"}">{html.escape(m.group(2))}</code></pre>',
        html_content,
        flags=re.DOTALL
    )

    # Convert headers (skip the first one as it's handled in metadata)
    lines = html_content.split('\n')
    processed_lines = []
    first_h1_found = False

    for line in lines:
        if line.startswith('# ') and not first_h1_found:
            first_h1_found = True
            continue  # Skip the first h1 as it's in the header
        elif line.startswith('#### '):
            processed_lines.append(f'<h4>{line[5:]}</h4>')
        elif line.startswith('### '):
            processed_lines.append(f'<h3>{line[4:]}</h3>')
        elif line.startswith('## '):
            processed_lines.append(f'<h2>{line[3:]}</h2>')
        elif line.startswith('# '):
            processed_lines.append(f'<h1>{line[2:]}</h1>')
        else:
            processed_lines.append(line)

    html_content = '\n'.join(processed_lines)

    # Convert bold text
    html_content = re.sub(r'\*\*(.+?)\*\*', r'<strong>\1</strong>', html_content)

    # Convert italic text (simple approach - single asterisks not part of bold)
    html_content = re.sub(r'(?<!\*)\*([^*\n]+?)\*(?!\*)', r'<em>\1</em>', html_content)

    # Convert inline code (simple approach)
    html_content = re.sub(r'`([^`]+)`', r'<code>\1</code>', html_content)

    # Convert blockquotes
    html_content = re.sub(r'^> (.+)$', r'<blockquote>\1</blockquote>', html_content, flags=re.MULTILINE)

    # Convert links
    html_content = re.sub(r'\[([^\]]+)\]\(([^)]+)\)', r'<a href="\2">\1</a>', html_content)
    
    # Convert lists and paragraphs
    lines = html_content.split('\n')
    in_ul = False
    in_ol = False
    in_code_block = False
    result_lines = []

    for line in lines:
        stripped = line.strip()

        # Track code blocks to avoid processing content inside them
        if '<pre>' in line:
            in_code_block = True
        elif '</pre>' in line:
            in_code_block = False

        if in_code_block:
            result_lines.append(line)
            continue

        # Unordered list
        if stripped.startswith('- ') or stripped.startswith('* '):
            if not in_ul:
                result_lines.append('<ul>')
                in_ul = True
            if in_ol:
                result_lines.append('</ol>')
                in_ol = False
            result_lines.append(f'<li>{stripped[2:]}</li>')

        # Ordered list
        elif re.match(r'^\d+\. ', stripped):
            if not in_ol:
                result_lines.append('<ol>')
                in_ol = True
            if in_ul:
                result_lines.append('</ul>')
                in_ul = False
            content = re.sub(r'^\d+\. ', '', stripped)
            result_lines.append(f'<li>{content}</li>')

        else:
            if in_ul:
                result_lines.append('</ul>')
                in_ul = False
            if in_ol:
                result_lines.append('</ol>')
                in_ol = False

            # Convert paragraphs (but skip empty lines and HTML tags)
            if stripped and not stripped.startswith('<') and not stripped.startswith('**Category:**') and not stripped.startswith('**Points:**') and not stripped.startswith('**Solves:**') and not stripped.startswith('**Author:**'):
                result_lines.append(f'<p>{line}</p>')
            elif stripped.startswith('<') or not stripped:
                result_lines.append(line)

    # Close any remaining lists
    if in_ul:
        result_lines.append('</ul>')
    if in_ol:
        result_lines.append('</ol>')

    return '\n'.join(result_lines)

def create_html_template(metadata, content):
    """Create complete HTML document"""

    # Extract flag if present (look for the actual flag, not test flags)
    flag_matches = re.findall(r'tjctf\{[^}]+\}', content)
    # Filter out test flags and find the real one
    real_flags = [f for f in flag_matches if 'test' not in f.lower() and len(f) > 15]
    flag = real_flags[0] if real_flags else (flag_matches[0] if flag_matches else "")
    
    # Create metadata section
    meta_items = []
    if metadata['category']:
        meta_items.append(f'<div class="meta-item"><span class="meta-label">Category:</span> {metadata["category"]}</div>')
    if metadata['points']:
        meta_items.append(f'<div class="meta-item"><span class="meta-label">Points:</span> {metadata["points"]}</div>')
    if metadata['solves']:
        meta_items.append(f'<div class="meta-item"><span class="meta-label">Solves:</span> {metadata["solves"]}</div>')
    if metadata['author']:
        meta_items.append(f'<div class="meta-item"><span class="meta-label">Author:</span> {metadata["author"]}</div>')
    
    meta_html = '\n'.join(meta_items)
    
    # Add flag section if flag exists
    flag_html = f'<div class="flag">🚩 {flag}</div>' if flag else ""
    
    return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{metadata['title']} - TRIADA CTF Writeup</title>
    <link rel="stylesheet" href="writeup-styles.css">
</head>
<body>
    <nav class="writeup-nav">
        <a href="../../../index.html" class="back-btn">← Back to Home</a>
    </nav>
    
    <div class="writeup-container">
        <header class="writeup-header">
            <h1 class="writeup-title">{metadata['title']}</h1>
            <div class="challenge-meta">
                {meta_html}
            </div>
        </header>
        
        <main class="writeup-content">
            {content}
            {flag_html}
        </main>
    </div>
</body>
</html>"""

def convert_file(md_file_path, output_dir):
    """Convert a single markdown file to HTML"""
    with open(md_file_path, 'r', encoding='utf-8') as f:
        md_content = f.read()
    
    # Parse metadata
    metadata = parse_markdown_metadata(md_content)
    
    # Convert to HTML
    html_content = convert_markdown_to_html(md_content)
    
    # Create complete HTML document
    full_html = create_html_template(metadata, html_content)
    
    # Generate output filename
    output_filename = Path(md_file_path).stem + '.html'
    output_path = output_dir / output_filename
    
    # Write HTML file
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(full_html)
    
    print(f"Converted: {md_file_path} -> {output_path}")

def main():
    """Main conversion function"""
    # Get current directory and set paths
    current_dir = Path(__file__).parent
    writeups_dir = current_dir.parent
    output_dir = current_dir
    
    # Find all markdown files
    md_files = list(writeups_dir.glob('*.md'))
    
    if not md_files:
        print("No markdown files found in the writeups directory")
        return
    
    print(f"Found {len(md_files)} markdown files to convert")
    
    # Convert each file
    for md_file in md_files:
        try:
            convert_file(md_file, output_dir)
        except Exception as e:
            print(f"Error converting {md_file}: {e}")
    
    print(f"\nConversion complete! HTML files saved to: {output_dir}")

if __name__ == "__main__":
    main()
