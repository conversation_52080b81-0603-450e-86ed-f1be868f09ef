@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600&display=swap');

:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --dark: #1e293b;
    --darker: #0f172a;
    --light: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-sans);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    color: var(--gray-900);
    line-height: 1.7;
    font-size: 16px;
    min-height: 100vh;
}

.writeup-container {
    max-width: 900px;
    margin: 0 auto;
    background: white;
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* Header */
.writeup-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.writeup-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    letter-spacing: -0.025em;
}

.challenge-meta {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.meta-item {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.meta-label {
    font-weight: 600;
    opacity: 0.9;
}

/* Table of Contents */
.toc {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 2rem 0;
}

.toc h3 {
    color: var(--gray-800);
    margin-bottom: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
}

.toc ul {
    list-style: none;
    padding: 0;
}

.toc li {
    margin-bottom: 0.5rem;
}

.toc a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.toc a:hover {
    color: var(--secondary-color);
}

/* Content */
.writeup-content {
    padding: 2rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--gray-900);
    font-weight: 600;
    line-height: 1.3;
    margin: 2rem 0 1rem 0;
}

h1 { font-size: 2rem; }
h2 { 
    font-size: 1.75rem; 
    color: var(--primary-color);
    border-bottom: 2px solid var(--gray-200);
    padding-bottom: 0.5rem;
}
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

p {
    margin-bottom: 1rem;
    color: var(--gray-700);
}

/* Lists */
ul, ol {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

li {
    margin-bottom: 0.5rem;
    color: var(--gray-700);
}

/* Code */
code {
    font-family: var(--font-mono);
    background: var(--gray-100);
    border: 1px solid var(--gray-200);
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
    font-size: 0.875rem;
    color: var(--danger-color);
    font-weight: 500;
}

.code-block {
    margin: 1.5rem 0;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.code-block pre {
    background: var(--gray-900);
    color: var(--gray-100);
    padding: 1.5rem;
    overflow-x: auto;
    margin: 0;
    font-size: 0.875rem;
    line-height: 1.6;
}

.code-block code {
    background: none;
    border: none;
    padding: 0;
    color: inherit;
    font-size: inherit;
}

/* Syntax highlighting */
.language-python .keyword { color: #ff6b6b; }
.language-python .string { color: #4ecdc4; }
.language-python .comment { color: #6c757d; }
.language-python .number { color: #ffd93d; }
.language-bash .command { color: #4ecdc4; }

/* Blockquotes */
blockquote {
    border-left: 4px solid var(--primary-color);
    background: var(--gray-50);
    padding: 1rem 1.5rem;
    margin: 1.5rem 0;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    font-style: italic;
    color: var(--gray-600);
}

/* Links */
a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

/* Flag Section */
.flag-section {
    background: linear-gradient(135deg, var(--success-color) 0%, #**********%);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin: 2rem 0;
    text-align: center;
}

.flag-section h2 {
    color: white;
    border: none;
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
}

.flag {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    font-family: var(--font-mono);
    font-size: 1.125rem;
    font-weight: 600;
    letter-spacing: 0.025em;
    margin: 0;
}

/* Footer */
.writeup-footer {
    background: var(--gray-800);
    color: var(--gray-300);
    padding: 1.5rem 2rem;
    text-align: center;
    font-size: 0.875rem;
}

.writeup-footer p {
    margin: 0.25rem 0;
    color: inherit;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

th, td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-800);
}

/* Responsive */
@media (max-width: 768px) {
    .writeup-container {
        margin: 1rem;
        border-radius: var(--border-radius);
    }
    
    .writeup-header {
        padding: 1.5rem;
    }
    
    .writeup-title {
        font-size: 2rem;
    }
    
    .writeup-content {
        padding: 1.5rem;
    }
    
    .challenge-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .code-block pre {
        padding: 1rem;
        font-size: 0.8rem;
    }
}

/* Print styles */
@media print {
    body {
        background: white;
    }
    
    .writeup-container {
        box-shadow: none;
        max-width: none;
    }
    
    .writeup-header {
        background: var(--gray-800) !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    .code-block pre {
        background: var(--gray-100) !important;
        color: var(--gray-900) !important;
        border: 1px solid var(--gray-300);
    }
}
