# TJCTF 2024 Writeups - HTML Version

This folder contains HTML versions of all TJCTF 2024 writeups, converted from Markdown with custom styling inspired by the main website's cyberpunk aesthetic.

## Files Overview

### Core Files
- `index.html` - Main landing page with writeup grid and statistics
- `writeup-styles.css` - Custom CSS styling inspired by the main site's `styles.css`
- `convert_md_to_html.py` - Python script used to convert Markdown to HTML
- `README.md` - This documentation file

### Converted Writeups
- `alchemist-writeup.html` - Crypto challenge (331 pts, 59 solves)
- `artofwar-writeup.html` - Crypto challenge (259 pts, 135 solves)
- `close-secrets-writeup.html` - Crypto challenge
- `double-trouble-writeup.html` - Challenge writeup
- `extra-credit-writeup.html` - Challenge writeup
- `i-love-brids-writeup.html` - Challenge writeup
- `neural-inv-writeup.html` - Challenge writeup
- `packer-palette-writeup.html` - Challenge writeup

## Features

### Styling
- **Cyberpunk Aesthetic**: Dark theme with red accents matching the main site
- **Typography**: Uses <PERSON>serrat for body text, Technos for headers, JetBrains Mono for code
- **Responsive Design**: Mobile-friendly layout with adaptive grid systems
- **Syntax Highlighting**: Color-coded code blocks for Python, Bash, and other languages

### Content Structure
- **Header Section**: Challenge title, category, points, solves, and author
- **Navigation**: Back button to return to main site
- **Flag Display**: Highlighted flag section at the bottom
- **Code Blocks**: Properly formatted with syntax highlighting
- **Lists and Typography**: Clean formatting for readability

### Interactive Elements
- **Hover Effects**: Cards and buttons with smooth transitions
- **Glow Effects**: Neon-style borders and shadows
- **Grid Layout**: Responsive card grid for writeup browsing

## Technical Details

### CSS Variables
The styling uses CSS custom properties for consistent theming:
```css
--primary-red: #ff0033
--dark-red: #cc0026
--black: #0a0a0a
--white: #fff
--neon-border: 1px solid rgba(255, 0, 51, 0.3)
--glow-effect: 0 0 15px rgba(255, 0, 51, 0.5)
```

### Conversion Process
The `convert_md_to_html.py` script:
1. Parses Markdown metadata (title, category, points, etc.)
2. Converts Markdown syntax to HTML
3. Applies custom HTML template with styling
4. Extracts and highlights CTF flags
5. Generates complete HTML documents

### Responsive Breakpoints
- Desktop: Full grid layout with sidebar navigation
- Tablet (768px): Adjusted grid columns and spacing
- Mobile (576px): Single column layout with stacked elements

## Usage

### Viewing Writeups
1. Open `index.html` in a web browser
2. Browse the writeup grid
3. Click on any writeup card to view the full solution

### Converting New Writeups
1. Place new `.md` files in the parent `writeups` directory
2. Run `python convert_md_to_html.py` from this directory
3. New HTML files will be generated automatically

### Customizing Styles
Edit `writeup-styles.css` to modify:
- Color scheme and theming
- Typography and fonts
- Layout and spacing
- Interactive effects

## Browser Compatibility
- Chrome/Chromium (recommended)
- Firefox
- Safari
- Edge

## Dependencies
- Python 3.x (for conversion script)
- Modern web browser with CSS Grid support
- Internet connection (for Google Fonts)

## File Structure
```
writeup_html/
├── index.html              # Main landing page
├── writeup-styles.css      # Custom styling
├── convert_md_to_html.py   # Conversion script
├── README.md              # Documentation
├── alchemist-writeup.html # Individual writeups...
├── artofwar-writeup.html
└── [other writeup files]
```

## Credits
- **Team**: TRIADA CTF Team
- **Competition**: TJCTF 2024
- **Styling**: Inspired by main site's cyberpunk aesthetic
- **Fonts**: Technos, Montserrat, JetBrains Mono

## Notes
- All writeups maintain their original content and structure
- Code blocks preserve syntax and formatting
- Flags are automatically detected and highlighted
- Navigation links point back to the main site structure
